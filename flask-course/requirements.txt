# Flask Course Requirements

# Core Flask
Flask==2.3.3
Werkzeug==2.3.7

# Templates
Jinja2==3.1.2
MarkupSafe==2.1.3

# Forms
Flask-WTF==1.1.1
WTForms==3.0.1
email-validator==2.0.0

# Database
Flask-SQLAlchemy==3.0.5
Flask-Migrate==4.0.5

# Authentication
Flask-Login==0.6.3
bcrypt==4.0.1

# Sessions and Security
itsdangerous==2.1.2

# File handling
Pillow==10.0.0

# Development tools
python-dotenv==1.0.0

# Testing
pytest==7.4.0
pytest-flask==1.2.0

# API development
Flask-RESTful==0.3.10
marshmallow==3.20.1

# Mail
Flask-Mail==0.9.1

# Caching
Flask-Caching==2.1.0

# Admin interface
Flask-Admin==1.6.1

# CLI tools
click==8.1.7

# HTTP client for testing
requests==2.31.0

# Database drivers
# SQLite is included with Python
# For PostgreSQL: psycopg2-binary==2.9.7
# For MySQL: PyMySQL==1.1.0

# Production server
gunicorn==21.2.0

# Environment management
python-decouple==3.8
