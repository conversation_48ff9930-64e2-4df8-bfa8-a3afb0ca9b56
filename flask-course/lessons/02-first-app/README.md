# Lesson 2: Your First Flask App

## 🎯 Learning Objectives
By the end of this lesson, you will:
- Create your first Flask application
- Understand Flask app structure
- Learn about routes and view functions
- Run a Flask development server
- Understand the request-response cycle

## 🚀 Creating Your First Flask App

Let's create the simplest possible Flask application:

### Step 1: Create `app.py`

```python
from flask import Flask

# Create Flask application instance
app = Flask(__name__)

# Define a route
@app.route('/')
def hello_world():
    return 'Hello, World!'

# Run the application
if __name__ == '__main__':
    app.run(debug=True)
```

### Step 2: Run the Application

```bash
python app.py
```

You should see output like:
```
 * Running on http://127.0.0.1:5000
 * Debug mode: on
```

### Step 3: View in Browser

Open your browser and go to `http://127.0.0.1:5000` or `http://localhost:5000`

You should see "Hello, World!" displayed.

## 🔍 Understanding the Code

Let's break down each part:

### 1. Import Flask
```python
from flask import Flask
```
This imports the Flask class from the flask module.

### 2. Create Application Instance
```python
app = Flask(__name__)
```
- `Flask(__name__)` creates an instance of the Flask class
- `__name__` is a Python variable that contains the name of the current module
- This helps Flask locate resources like templates and static files

### 3. Define Routes
```python
@app.route('/')
def hello_world():
    return 'Hello, World!'
```
- `@app.route('/')` is a **decorator** that tells Flask what URL should trigger this function
- `'/'` is the root URL (homepage)
- `hello_world()` is a **view function** that returns the response
- The return value becomes the HTTP response sent to the browser

### 4. Run the Application
```python
if __name__ == '__main__':
    app.run(debug=True)
```
- This runs the Flask development server
- `debug=True` enables debug mode (auto-reload, detailed error messages)
- Only runs when the script is executed directly (not imported)

## 🛣️ Understanding Routes

Routes are URL patterns that Flask uses to determine which function to call:

```python
from flask import Flask

app = Flask(__name__)

@app.route('/')
def home():
    return 'Welcome to the homepage!'

@app.route('/about')
def about():
    return 'This is the about page!'

@app.route('/contact')
def contact():
    return 'Contact us at: <EMAIL>'

if __name__ == '__main__':
    app.run(debug=True)
```

Now you can visit:
- `http://localhost:5000/` → "Welcome to the homepage!"
- `http://localhost:5000/about` → "This is the about page!"
- `http://localhost:5000/contact` → "Contact us at: <EMAIL>"

## 🔄 The Request-Response Cycle

Here's what happens when you visit a Flask route:

```
1. Browser sends HTTP request to Flask server
   ↓
2. Flask receives request and matches URL to route
   ↓
3. Flask calls the corresponding view function
   ↓
4. View function processes request and returns response
   ↓
5. Flask sends HTTP response back to browser
   ↓
6. Browser displays the response
```

## 🎨 Returning Different Content Types

### HTML Content
```python
@app.route('/html')
def html_content():
    return '<h1>This is HTML!</h1><p>With <strong>formatting</strong></p>'
```

### JSON Content
```python
from flask import jsonify

@app.route('/json')
def json_content():
    return jsonify({
        'message': 'Hello, JSON!',
        'status': 'success',
        'data': [1, 2, 3, 4, 5]
    })
```

### Custom Status Codes
```python
@app.route('/not-found')
def not_found():
    return 'This page was not found!', 404

@app.route('/redirect-me')
def redirect_me():
    return 'Redirecting...', 302, {'Location': '/'}
```

## ⚙️ Configuration and Debug Mode

### Debug Mode Benefits:
1. **Auto-reload**: Server restarts when code changes
2. **Interactive debugger**: Detailed error pages with code inspection
3. **Better error messages**: More informative error output

### Different Ways to Enable Debug Mode:

#### Method 1: In code
```python
app.run(debug=True)
```

#### Method 2: Environment variable
```bash
export FLASK_DEBUG=1  # On Windows: set FLASK_DEBUG=1
flask run
```

#### Method 3: Using flask command
```bash
flask --app app.py --debug run
```

## 🏗️ Application Structure

As your app grows, organize it better:

```python
from flask import Flask

def create_app():
    """Application factory pattern"""
    app = Flask(__name__)
    
    # Configuration
    app.config['DEBUG'] = True
    app.config['SECRET_KEY'] = 'your-secret-key-here'
    
    # Routes
    @app.route('/')
    def home():
        return 'Welcome to our app!'
    
    @app.route('/health')
    def health_check():
        return {'status': 'healthy', 'version': '1.0.0'}
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run()
```

## 🧪 Practical Examples

### Example 1: Personal Website
```python
from flask import Flask
from datetime import datetime

app = Flask(__name__)

@app.route('/')
def home():
    return '''
    <h1>Welcome to My Website!</h1>
    <nav>
        <a href="/about">About</a> |
        <a href="/projects">Projects</a> |
        <a href="/contact">Contact</a>
    </nav>
    '''

@app.route('/about')
def about():
    return '''
    <h1>About Me</h1>
    <p>I'm a Flask developer learning web development!</p>
    <a href="/">← Back to Home</a>
    '''

@app.route('/projects')
def projects():
    return '''
    <h1>My Projects</h1>
    <ul>
        <li>Flask Learning App</li>
        <li>Personal Blog</li>
        <li>Todo List App</li>
    </ul>
    <a href="/">← Back to Home</a>
    '''

@app.route('/time')
def current_time():
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    return f'<h1>Current Time</h1><p>{now}</p>'

if __name__ == '__main__':
    app.run(debug=True)
```

### Example 2: Simple API
```python
from flask import Flask, jsonify

app = Flask(__name__)

# Sample data
users = [
    {'id': 1, 'name': 'Alice', 'email': '<EMAIL>'},
    {'id': 2, 'name': 'Bob', 'email': '<EMAIL>'},
    {'id': 3, 'name': 'Charlie', 'email': '<EMAIL>'}
]

@app.route('/api/users')
def get_users():
    return jsonify(users)

@app.route('/api/users/<int:user_id>')
def get_user(user_id):
    user = next((u for u in users if u['id'] == user_id), None)
    if user:
        return jsonify(user)
    return jsonify({'error': 'User not found'}), 404

@app.route('/api/status')
def api_status():
    return jsonify({
        'status': 'online',
        'version': '1.0.0',
        'total_users': len(users)
    })

if __name__ == '__main__':
    app.run(debug=True)
```

## 🔧 Development Tips

### 1. Use Environment Variables
```python
import os
from flask import Flask

app = Flask(__name__)

# Get configuration from environment
app.config['DEBUG'] = os.environ.get('FLASK_DEBUG', False)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-key')

@app.route('/')
def home():
    env = os.environ.get('FLASK_ENV', 'development')
    return f'Running in {env} mode'
```

### 2. Handle Errors Gracefully
```python
@app.errorhandler(404)
def page_not_found(error):
    return '<h1>Page Not Found</h1><p>The page you requested does not exist.</p>', 404

@app.errorhandler(500)
def internal_error(error):
    return '<h1>Internal Server Error</h1><p>Something went wrong!</p>', 500
```

## 📝 Exercises

### Exercise 1: Personal Portfolio
Create a Flask app with these routes:
- `/` - Homepage with your name and navigation
- `/about` - About page with your bio
- `/skills` - List of your programming skills
- `/contact` - Contact information

### Exercise 2: Simple Calculator API
Create routes that perform calculations:
- `/add/<int:a>/<int:b>` - Returns sum of a and b
- `/multiply/<int:a>/<int:b>` - Returns product of a and b
- `/square/<int:n>` - Returns square of n

### Exercise 3: Dynamic Content
Create a route that shows:
- Current date and time
- Random number between 1-100
- Visitor counter (use a global variable)

## ❓ Common Issues

**Issue**: `ModuleNotFoundError: No module named 'flask'`
**Solution**: Make sure Flask is installed and virtual environment is activated

**Issue**: `Address already in use`
**Solution**: Use a different port: `app.run(port=5001)`

**Issue**: Changes not reflecting
**Solution**: Make sure debug mode is enabled

**Issue**: `ImportError` when running with `flask run`
**Solution**: Set `FLASK_APP` environment variable: `export FLASK_APP=app.py`

## ✅ Lesson Checklist

- [ ] Created and ran your first Flask app
- [ ] Understand Flask app structure
- [ ] Created multiple routes
- [ ] Returned different content types
- [ ] Enabled debug mode
- [ ] Handled basic errors
- [ ] Completed exercises

---

**Next**: [Lesson 3: Routing and URL Building](../03-routing/README.md)

In the next lesson, we'll dive deeper into Flask routing, including dynamic routes, URL parameters, and URL building.
