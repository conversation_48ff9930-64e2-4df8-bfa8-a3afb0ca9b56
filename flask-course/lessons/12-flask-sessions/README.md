# Lesson 12: Flask Sessions

## 🎯 Learning Objectives
By the end of this lesson, you will:
- Implement sessions in Flask applications
- Store and retrieve session data
- Configure session settings
- Handle session security
- Build practical session-based features

## 🔧 Setting Up Flask Sessions

### Basic Session Setup
```python
from flask import Flask, session, request, redirect, url_for

app = Flask(__name__)

# REQUIRED: Set a secret key for session security
app.secret_key = 'your-very-secret-key-here'

@app.route('/')
def home():
    return 'Welcome to Flask Sessions!'

if __name__ == '__main__':
    app.run(debug=True)
```

### Important: Secret Key Security
```python
import os
import secrets

# Method 1: Environment variable (recommended)
app.secret_key = os.environ.get('SECRET_KEY') or 'fallback-key'

# Method 2: Generate random key
app.secret_key = secrets.token_hex(16)

# Method 3: From file
try:
    with open('secret_key.txt', 'r') as f:
        app.secret_key = f.read().strip()
except FileNotFoundError:
    app.secret_key = secrets.token_hex(16)
    with open('secret_key.txt', 'w') as f:
        f.write(app.secret_key)
```

## 📝 Working with Session Data

### Storing Data in Sessions
```python
from flask import Flask, session, request, render_template_string

app = Flask(__name__)
app.secret_key = 'demo-secret-key'

@app.route('/')
def home():
    # Store different types of data
    session['username'] = 'alice'
    session['user_id'] = 42
    session['is_logged_in'] = True
    session['preferences'] = {
        'theme': 'dark',
        'language': 'en'
    }
    session['cart_items'] = [1, 2, 3]
    
    return 'Session data stored!'

@app.route('/view-session')
def view_session():
    return f'''
    <h1>Session Data</h1>
    <ul>
        <li>Username: {session.get('username', 'Not set')}</li>
        <li>User ID: {session.get('user_id', 'Not set')}</li>
        <li>Logged in: {session.get('is_logged_in', False)}</li>
        <li>Preferences: {session.get('preferences', {})}</li>
        <li>Cart items: {session.get('cart_items', [])}</li>
    </ul>
    <a href="/clear-session">Clear Session</a>
    '''

@app.route('/clear-session')
def clear_session():
    session.clear()
    return 'Session cleared! <a href="/view-session">View Session</a>'
```

### Session Methods and Properties
```python
# Check if key exists
if 'username' in session:
    print(f"User: {session['username']}")

# Get with default value
username = session.get('username', 'Guest')

# Set session data
session['key'] = 'value'

# Remove specific key
session.pop('key', None)  # None is default if key doesn't exist

# Clear all session data
session.clear()

# Mark session as modified (for mutable objects)
session['cart'].append(new_item)
session.modified = True

# Make session permanent (survives browser restart)
session.permanent = True
```

## 🛡️ Session Configuration

### Session Configuration Options
```python
from datetime import timedelta

app = Flask(__name__)
app.secret_key = 'your-secret-key'

# Session configuration
app.config.update(
    # Session lifetime (default: browser session)
    PERMANENT_SESSION_LIFETIME=timedelta(days=7),
    
    # Session cookie settings
    SESSION_COOKIE_NAME='my_session',
    SESSION_COOKIE_DOMAIN=None,  # Use default domain
    SESSION_COOKIE_PATH='/',     # Available for entire site
    SESSION_COOKIE_HTTPONLY=True,  # Prevent JavaScript access
    SESSION_COOKIE_SECURE=False,   # Set to True for HTTPS
    SESSION_COOKIE_SAMESITE='Lax', # CSRF protection
)

@app.route('/permanent-session')
def permanent_session():
    session.permanent = True  # Session will last for PERMANENT_SESSION_LIFETIME
    session['user'] = 'alice'
    return 'Permanent session created!'
```

### Production Security Settings
```python
# Production configuration
app.config.update(
    SESSION_COOKIE_SECURE=True,      # Only send over HTTPS
    SESSION_COOKIE_HTTPONLY=True,    # Prevent XSS attacks
    SESSION_COOKIE_SAMESITE='Strict', # Strong CSRF protection
)
```

## 🧪 Practical Examples

### Example 1: Simple Login System
```python
from flask import Flask, session, request, redirect, url_for, render_template_string

app = Flask(__name__)
app.secret_key = 'demo-secret-key'

# Mock user database
users = {
    'alice': 'password123',
    'bob': 'secret456'
}

LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head><title>Login</title></head>
<body>
    <h1>Login</h1>
    {% if error %}
        <p style="color: red;">{{ error }}</p>
    {% endif %}
    <form method="POST">
        <p>
            <label>Username:</label><br>
            <input type="text" name="username" required>
        </p>
        <p>
            <label>Password:</label><br>
            <input type="password" name="password" required>
        </p>
        <p>
            <input type="submit" value="Login">
        </p>
    </form>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head><title>Dashboard</title></head>
<body>
    <h1>Welcome, {{ username }}!</h1>
    <p>You are logged in.</p>
    <p>Session data: {{ session_data }}</p>
    <a href="{{ url_for('logout') }}">Logout</a>
</body>
</html>
'''

@app.route('/')
def home():
    if 'username' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        if username in users and users[username] == password:
            session['username'] = username
            session['logged_in'] = True
            return redirect(url_for('dashboard'))
        else:
            return render_template_string(LOGIN_TEMPLATE, error='Invalid credentials')
    
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/dashboard')
def dashboard():
    if 'username' not in session:
        return redirect(url_for('login'))
    
    return render_template_string(DASHBOARD_TEMPLATE, 
                                username=session['username'],
                                session_data=dict(session))

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

if __name__ == '__main__':
    app.run(debug=True)
```

### Example 2: Shopping Cart
```python
from flask import Flask, session, request, jsonify, render_template_string

app = Flask(__name__)
app.secret_key = 'cart-demo-key'

# Mock product database
products = {
    1: {'name': 'Laptop', 'price': 999.99},
    2: {'name': 'Mouse', 'price': 29.99},
    3: {'name': 'Keyboard', 'price': 79.99},
    4: {'name': 'Monitor', 'price': 299.99}
}

SHOP_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>Shopping Cart Demo</title>
    <style>
        .product { border: 1px solid #ccc; margin: 10px; padding: 10px; }
        .cart { background: #f0f0f0; padding: 10px; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>Online Shop</h1>
    
    <div class="cart">
        <h2>Shopping Cart ({{ cart_count }} items)</h2>
        {% if cart_items %}
            <ul>
            {% for item in cart_items %}
                <li>
                    {{ item.name }} - ${{ "%.2f"|format(item.price) }} 
                    (Qty: {{ item.quantity }})
                    <a href="{{ url_for('remove_from_cart', product_id=item.id) }}">Remove</a>
                </li>
            {% endfor %}
            </ul>
            <p><strong>Total: ${{ "%.2f"|format(cart_total) }}</strong></p>
            <a href="{{ url_for('clear_cart') }}">Clear Cart</a>
        {% else %}
            <p>Your cart is empty</p>
        {% endif %}
    </div>
    
    <h2>Products</h2>
    {% for product_id, product in products.items() %}
        <div class="product">
            <h3>{{ product.name }}</h3>
            <p>Price: ${{ "%.2f"|format(product.price) }}</p>
            <a href="{{ url_for('add_to_cart', product_id=product_id) }}">Add to Cart</a>
        </div>
    {% endfor %}
</body>
</html>
'''

@app.route('/')
def shop():
    cart = session.get('cart', {})
    cart_items = []
    cart_total = 0
    
    for product_id, quantity in cart.items():
        product = products.get(int(product_id))
        if product:
            item = {
                'id': int(product_id),
                'name': product['name'],
                'price': product['price'],
                'quantity': quantity
            }
            cart_items.append(item)
            cart_total += product['price'] * quantity
    
    return render_template_string(SHOP_TEMPLATE,
                                products=products,
                                cart_items=cart_items,
                                cart_count=sum(cart.values()),
                                cart_total=cart_total)

@app.route('/add-to-cart/<int:product_id>')
def add_to_cart(product_id):
    if product_id not in products:
        return 'Product not found', 404
    
    if 'cart' not in session:
        session['cart'] = {}
    
    cart = session['cart']
    product_id_str = str(product_id)
    
    if product_id_str in cart:
        cart[product_id_str] += 1
    else:
        cart[product_id_str] = 1
    
    session['cart'] = cart  # Ensure session is marked as modified
    return redirect(url_for('shop'))

@app.route('/remove-from-cart/<int:product_id>')
def remove_from_cart(product_id):
    if 'cart' in session:
        cart = session['cart']
        product_id_str = str(product_id)
        
        if product_id_str in cart:
            if cart[product_id_str] > 1:
                cart[product_id_str] -= 1
            else:
                del cart[product_id_str]
            
            session['cart'] = cart
    
    return redirect(url_for('shop'))

@app.route('/clear-cart')
def clear_cart():
    session.pop('cart', None)
    return redirect(url_for('shop'))

@app.route('/api/cart')
def api_cart():
    """API endpoint to get cart data as JSON"""
    cart = session.get('cart', {})
    return jsonify(cart)

if __name__ == '__main__':
    app.run(debug=True)
```

### Example 3: Multi-Step Form with Session
```python
from flask import Flask, session, request, redirect, url_for, render_template_string

app = Flask(__name__)
app.secret_key = 'multistep-form-key'

STEP1_TEMPLATE = '''
<h1>Step 1: Personal Information</h1>
<form method="POST">
    <p><label>First Name:</label><br><input type="text" name="first_name" required></p>
    <p><label>Last Name:</label><br><input type="text" name="last_name" required></p>
    <p><label>Email:</label><br><input type="email" name="email" required></p>
    <p><input type="submit" value="Next Step"></p>
</form>
'''

STEP2_TEMPLATE = '''
<h1>Step 2: Address Information</h1>
<p>Hello {{ session.step1.first_name }}!</p>
<form method="POST">
    <p><label>Street Address:</label><br><input type="text" name="address" required></p>
    <p><label>City:</label><br><input type="text" name="city" required></p>
    <p><label>ZIP Code:</label><br><input type="text" name="zip_code" required></p>
    <p>
        <a href="{{ url_for('step1') }}">← Previous</a>
        <input type="submit" value="Next Step">
    </p>
</form>
'''

STEP3_TEMPLATE = '''
<h1>Step 3: Review Information</h1>
<h2>Personal Information</h2>
<ul>
    <li>Name: {{ session.step1.first_name }} {{ session.step1.last_name }}</li>
    <li>Email: {{ session.step1.email }}</li>
</ul>
<h2>Address Information</h2>
<ul>
    <li>Address: {{ session.step2.address }}</li>
    <li>City: {{ session.step2.city }}</li>
    <li>ZIP: {{ session.step2.zip_code }}</li>
</ul>
<form method="POST">
    <p>
        <a href="{{ url_for('step2') }}">← Previous</a>
        <input type="submit" value="Submit">
    </p>
</form>
'''

@app.route('/')
def home():
    return redirect(url_for('step1'))

@app.route('/step1', methods=['GET', 'POST'])
def step1():
    if request.method == 'POST':
        session['step1'] = {
            'first_name': request.form['first_name'],
            'last_name': request.form['last_name'],
            'email': request.form['email']
        }
        return redirect(url_for('step2'))
    
    return render_template_string(STEP1_TEMPLATE)

@app.route('/step2', methods=['GET', 'POST'])
def step2():
    if 'step1' not in session:
        return redirect(url_for('step1'))
    
    if request.method == 'POST':
        session['step2'] = {
            'address': request.form['address'],
            'city': request.form['city'],
            'zip_code': request.form['zip_code']
        }
        return redirect(url_for('step3'))
    
    return render_template_string(STEP2_TEMPLATE, session=session)

@app.route('/step3', methods=['GET', 'POST'])
def step3():
    if 'step1' not in session or 'step2' not in session:
        return redirect(url_for('step1'))
    
    if request.method == 'POST':
        # Process the complete form data
        complete_data = {**session['step1'], **session['step2']}
        
        # Here you would typically save to database
        print("Form submitted:", complete_data)
        
        # Clear session data
        session.pop('step1', None)
        session.pop('step2', None)
        
        return '<h1>Thank you!</h1><p>Your information has been submitted.</p><a href="/">Start Over</a>'
    
    return render_template_string(STEP3_TEMPLATE, session=session)

if __name__ == '__main__':
    app.run(debug=True)
```

## 🔍 Session Debugging and Monitoring

### Debug Session Data
```python
@app.route('/debug/session')
def debug_session():
    if not app.debug:
        return 'Debug mode only', 403
    
    return {
        'session_data': dict(session),
        'session_id': request.cookies.get(app.session_cookie_name),
        'session_permanent': session.permanent,
        'session_new': session.new,
        'session_modified': session.modified
    }

@app.before_request
def log_session():
    if app.debug:
        print(f"Session before request: {dict(session)}")

@app.after_request
def log_session_after(response):
    if app.debug:
        print(f"Session after request: {dict(session)}")
    return response
```

## 📝 Best Practices

1. **Always set a strong secret key**
2. **Use session.permanent for long-lived sessions**
3. **Clear sensitive data after use**
4. **Validate session data before using**
5. **Use HTTPS in production**
6. **Set appropriate cookie flags**
7. **Implement session timeout**
8. **Monitor session usage**

## ❓ Common Issues

**Issue**: Session data not persisting
**Solution**: Make sure secret key is set and consistent

**Issue**: Session data lost after server restart
**Solution**: Use permanent sessions or external session storage

**Issue**: "Session is not JSON serializable"
**Solution**: Only store JSON-serializable data (strings, numbers, lists, dicts)

**Issue**: Session data not updating
**Solution**: Use `session.modified = True` for mutable objects

## ✅ Lesson Checklist

- [ ] Set up Flask sessions with secret key
- [ ] Store and retrieve session data
- [ ] Configure session settings
- [ ] Implement login/logout system
- [ ] Build shopping cart with sessions
- [ ] Create multi-step form
- [ ] Debug session issues

---

**Next**: [Lesson 13: Cookies in Flask](../13-cookies/README.md)

In the next lesson, we'll explore cookies in detail and learn how to work with them directly in Flask.
