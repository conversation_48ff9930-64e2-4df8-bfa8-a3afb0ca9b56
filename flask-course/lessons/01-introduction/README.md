# Lesson 1: Introduction to Flask

## 🎯 Learning Objectives
By the end of this lesson, you will:
- Understand what Flask is and why it's popular
- Know the advantages and disadvantages of Flask
- Set up your development environment
- Understand Flask's philosophy and architecture

## 📖 What is Flask?

Flask is a lightweight and flexible Python web framework that provides the basic tools and libraries to build web applications. It's called a "micro" framework because it doesn't require particular tools or libraries and keeps the core simple but extensible.

### Key Characteristics:
- **Minimalist**: Provides only the essentials
- **Flexible**: Doesn't make many decisions for you
- **Extensible**: Easy to add functionality through extensions
- **Pythonic**: Follows Python conventions and idioms

## 🌟 Why Choose Flask?

### Advantages:
1. **Simple and Easy to Learn**: Minimal boilerplate code
2. **Flexible**: No rigid structure imposed
3. **Lightweight**: Small footprint and fast
4. **Extensible**: Rich ecosystem of extensions
5. **Well Documented**: Excellent documentation and community
6. **Testing Friendly**: Built-in testing support
7. **Jinja2 Templates**: Powerful templating engine
8. **Werkzeug Integration**: Robust WSGI toolkit

### Disadvantages:
1. **More Setup Required**: Need to make more decisions
2. **Can Become Complex**: Large applications need careful structure
3. **Less Built-in Features**: Compared to full-stack frameworks like Django

## 🏗️ Flask Architecture

Flask follows the **WSGI** (Web Server Gateway Interface) standard and has these core components:

```
┌─────────────────┐
│   Web Browser   │
└─────────┬───────┘
          │ HTTP Request
          ▼
┌─────────────────┐
│   Web Server    │ (Apache, Nginx, etc.)
└─────────┬───────┘
          │ WSGI
          ▼
┌─────────────────┐
│  Flask App      │
│  ┌───────────┐  │
│  │  Routes   │  │
│  ├───────────┤  │
│  │ Templates │  │
│  ├───────────┤  │
│  │  Models   │  │
│  └───────────┘  │
└─────────┬───────┘
          │ HTTP Response
          ▼
┌─────────────────┐
│   Web Browser   │
└─────────────────┘
```

## 🛠️ Development Environment Setup

### 1. Check Python Installation
```bash
python --version
# Should be Python 3.8 or higher
```

### 2. Create Project Directory
```bash
mkdir flask-learning
cd flask-learning
```

### 3. Create Virtual Environment
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### 4. Install Flask
```bash
pip install Flask
```

### 5. Verify Installation
```bash
python -c "import flask; print(f'Flask version: {flask.__version__}')"
```

## 📁 Project Structure

For this course, we'll use this structure:

```
flask-learning/
├── venv/                 # Virtual environment
├── app.py               # Main application file
├── config.py            # Configuration settings
├── requirements.txt     # Dependencies
├── templates/           # HTML templates
│   └── base.html
├── static/             # CSS, JS, images
│   ├── css/
│   ├── js/
│   └── images/
└── instance/           # Instance-specific files
```

## 🔧 Development Tools

### Recommended Tools:
1. **Code Editor**: VS Code, PyCharm, or Sublime Text
2. **Browser**: Chrome or Firefox with developer tools
3. **Terminal**: Command line interface
4. **Git**: Version control (optional but recommended)

### VS Code Extensions:
- Python
- Flask Snippets
- Jinja2
- HTML CSS Support

## 🎨 Flask Philosophy

Flask follows these principles:

1. **Explicit is better than implicit**
2. **Simple is better than complex**
3. **Flat is better than nested**
4. **Sparse is better than dense**

This means:
- You write what you need
- Configuration is explicit
- No magic behind the scenes
- Clean, readable code

## 🔍 Flask vs Other Frameworks

| Feature | Flask | Django | FastAPI |
|---------|-------|--------|---------|
| Learning Curve | Easy | Moderate | Easy-Moderate |
| Structure | Flexible | Opinionated | Flexible |
| Built-in Features | Minimal | Comprehensive | API-focused |
| Database ORM | Optional | Built-in | Optional |
| Admin Interface | Extension | Built-in | None |
| Best For | Small-Medium apps | Large apps | APIs |

## 📚 Core Concepts Preview

In upcoming lessons, we'll cover:

1. **Routing**: Mapping URLs to functions
2. **Templates**: Generating dynamic HTML
3. **Forms**: Handling user input
4. **Sessions**: Maintaining user state
5. **Databases**: Storing and retrieving data
6. **Authentication**: User login/logout
7. **APIs**: Building web services

## 🧪 Quick Test

Let's verify everything is working:

Create `test_setup.py`:

```python
import flask
import sys

print(f"Python version: {sys.version}")
print(f"Flask version: {flask.__version__}")
print("✅ Setup successful!")
```

Run it:
```bash
python test_setup.py
```

## 📝 Exercise

1. **Set up your environment** following the steps above
2. **Create the project structure** as shown
3. **Install Flask** and verify the installation
4. **Research**: Find 3 popular websites built with Flask

## 🔗 Additional Resources

- [Flask Official Documentation](https://flask.palletsprojects.com/)
- [Flask Mega-Tutorial](https://blog.miguelgrinberg.com/post/the-flask-mega-tutorial-part-i-hello-world)
- [Real Python Flask Tutorials](https://realpython.com/tutorials/flask/)

## ❓ Common Issues

**Issue**: `pip: command not found`
**Solution**: Make sure Python is properly installed and added to PATH

**Issue**: `Permission denied` when installing packages
**Solution**: Use virtual environment or `pip install --user`

**Issue**: Virtual environment not activating
**Solution**: Check the activation command for your OS

## ✅ Lesson Checklist

- [ ] Python 3.8+ installed
- [ ] Virtual environment created and activated
- [ ] Flask installed successfully
- [ ] Project structure created
- [ ] Development tools set up
- [ ] Understanding of Flask philosophy

---

**Next**: [Lesson 2: Your First Flask App](../02-first-app/README.md)

In the next lesson, we'll create our first Flask application and understand the basic concepts of routing and responses.
