import re
from wtforms.validators import ValidationError

def validate_phone(form, field):
    """Validate phone number format"""
    phone_regex = re.compile(r'^\+?1?\d{9,15}$')
    if not phone_regex.match(field.data.replace(' ', '').replace('-', '')):
        raise ValidationError('Please enter a valid phone number.')

def validate_postal_code(form, field):
    """Validate postal code format"""
    # Basic validation for various postal code formats
    postal_regex = re.compile(r'^[A-Za-z0-9\s-]{3,10}$')
    if not postal_regex.match(field.data):
        raise ValidationError('Please enter a valid postal code.')

def validate_password_strength(form, field):
    """Validate password strength"""
    password = field.data
    
    if len(password) < 8:
        raise ValidationError('Password must be at least 8 characters long.')
    
    if not re.search(r'[A-Z]', password):
        raise ValidationError('Password must contain at least one uppercase letter.')
    
    if not re.search(r'[a-z]', password):
        raise ValidationError('Password must contain at least one lowercase letter.')
    
    if not re.search(r'\d', password):
        raise ValidationError('Password must contain at least one number.')

def validate_sku(form, field):
    """Validate SKU format"""
    sku_regex = re.compile(r'^[A-Za-z0-9-_]{3,20}$')
    if not sku_regex.match(field.data):
        raise ValidationError('SKU must be 3-20 characters long and contain only letters, numbers, hyphens, and underscores.')
