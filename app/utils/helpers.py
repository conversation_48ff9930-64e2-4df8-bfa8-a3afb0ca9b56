import os
import secrets
import re
from PIL import Image
from flask import current_app, url_for
from werkzeug.utils import secure_filename

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

def save_picture(form_picture, folder, size=None):
    """Save uploaded picture with random filename"""
    random_hex = secrets.token_hex(8)
    _, f_ext = os.path.splitext(form_picture.filename)
    picture_fn = random_hex + f_ext
    picture_path = os.path.join(current_app.root_path, 'static', 'uploads', folder, picture_fn)
    
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(picture_path), exist_ok=True)
    
    if size:
        # Resize image
        img = Image.open(form_picture)
        img.thumbnail(size)
        img.save(picture_path)
    else:
        form_picture.save(picture_path)
    
    return picture_fn

def create_slug(text):
    """Create URL-friendly slug from text"""
    # Convert to lowercase and replace spaces with hyphens
    slug = re.sub(r'[^\w\s-]', '', text.lower())
    slug = re.sub(r'[-\s]+', '-', slug)
    return slug.strip('-')

def format_currency(amount):
    """Format amount as currency"""
    return f"${amount:,.2f}"

def calculate_tax(amount, tax_rate=0.08):
    """Calculate tax amount"""
    return amount * tax_rate

def calculate_shipping(weight, method='standard'):
    """Calculate shipping cost based on weight and method"""
    shipping_rates = {
        'standard': 5.99,
        'express': 12.99,
        'overnight': 24.99
    }
    
    base_rate = shipping_rates.get(method, 5.99)
    
    # Add weight-based charges (per kg)
    if weight > 1:
        base_rate += (weight - 1) * 2.50
    
    return base_rate

def generate_meta_description(text, max_length=160):
    """Generate meta description from text"""
    if len(text) <= max_length:
        return text
    
    # Truncate at word boundary
    truncated = text[:max_length]
    last_space = truncated.rfind(' ')
    if last_space > 0:
        truncated = truncated[:last_space]
    
    return truncated + '...'

def get_breadcrumbs(category=None, product=None):
    """Generate breadcrumbs for navigation"""
    breadcrumbs = [{'name': 'Home', 'url': url_for('main.index')}]
    
    if category:
        breadcrumbs.append({
            'name': 'Shop', 
            'url': url_for('shop.products')
        })
        breadcrumbs.append({
            'name': category.name,
            'url': url_for('shop.category', slug=category.slug)
        })
    
    if product:
        breadcrumbs.append({
            'name': product.name,
            'url': url_for('shop.product_detail', slug=product.slug)
        })
    
    return breadcrumbs

def paginate_query(query, page, per_page):
    """Paginate SQLAlchemy query"""
    return query.paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )

def get_cart_count(user):
    """Get cart item count for user"""
    if user.is_authenticated and user.cart:
        return user.cart.get_total_items()
    return 0

def send_email(to, subject, template, **kwargs):
    """Send email using Flask-Mail"""
    from flask_mail import Message
    from app import mail
    
    msg = Message(
        subject=subject,
        recipients=[to],
        html=template,
        sender=current_app.config['MAIL_DEFAULT_SENDER']
    )
    mail.send(msg)
