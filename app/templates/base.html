<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    
    <!-- SEO Meta Tags -->
    {% if title %}
        <title>{{ title }} - {{ config.SITE_NAME }}</title>
    {% else %}
        <title>{{ config.SITE_NAME }}</title>
    {% endif %}
    
    <meta name="description" content="{% block meta_description %}{{ config.SITE_DESCRIPTION }}{% endblock %}">
    <meta name="keywords" content="{% block meta_keywords %}{{ config.SITE_KEYWORDS }}{% endblock %}">
    <meta name="author" content="{{ config.SITE_NAME }}">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{% if title %}{{ title }} - {{ config.SITE_NAME }}{% else %}{{ config.SITE_NAME }}{% endif %}">
    <meta property="og:description" content="{% block og_description %}{{ config.SITE_DESCRIPTION }}{% endblock %}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request.url }}">
    <meta property="og:image" content="{% block og_image %}{{ url_for('static', filename='images/logo-og.png', _external=True) }}{% endblock %}">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{% if title %}{{ title }} - {{ config.SITE_NAME }}{% else %}{{ config.SITE_NAME }}{% endif %}">
    <meta name="twitter:description" content="{% block twitter_description %}{{ config.SITE_DESCRIPTION }}{% endblock %}">
    <meta name="twitter:image" content="{% block twitter_image %}{{ url_for('static', filename='images/logo-og.png', _external=True) }}{% endblock %}">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
    <link rel="apple-touch-icon" href="{{ url_for('static', filename='images/apple-touch-icon.png') }}">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
    
    <!-- Structured Data -->
    {% block structured_data %}
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "{{ config.SITE_NAME }}",
        "url": "{{ config.SITE_URL }}",
        "description": "{{ config.SITE_DESCRIPTION }}",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ config.SITE_URL }}/search?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>
    {% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="{{ url_for('main.index') }}">
                <i class="fas fa-store me-2"></i>FlaskShop
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}">Home</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            Shop
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('shop.products') }}">All Products</a></li>
                            <li><hr class="dropdown-divider"></li>
                            {% for category in nav_categories %}
                            <li><a class="dropdown-item" href="{{ url_for('shop.category', slug=category.slug) }}">{{ category.name }}</a></li>
                            {% endfor %}
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.about') }}">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.contact') }}">Contact</a>
                    </li>
                </ul>
                
                <!-- Search Form -->
                <form class="d-flex me-3" action="{{ url_for('main.search') }}" method="GET">
                    <div class="input-group">
                        <input class="form-control" type="search" name="q" placeholder="Search products..." 
                               value="{{ request.args.get('q', '') }}" autocomplete="off" id="searchInput">
                        <button class="btn btn-outline-light" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div id="searchResults" class="search-results"></div>
                </form>
                
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                        <li class="nav-item">
                            <a class="nav-link position-relative" href="{{ url_for('cart.view_cart') }}">
                                <i class="fas fa-shopping-cart"></i>
                                <span class="badge bg-danger rounded-pill position-absolute top-0 start-100 translate-middle" id="cartCount">
                                    {{ cart_count }}
                                </span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth.wishlist') }}">
                                <i class="fas fa-heart"></i>
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> {{ current_user.first_name }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">My Profile</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('auth.orders') }}">My Orders</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('auth.wishlist') }}">My Wishlist</a></li>
                                {% if current_user.is_admin %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('admin.dashboard') }}">Admin Dashboard</a></li>
                                {% endif %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">Logout</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('cart.view_cart') }}">
                                <i class="fas fa-shopping-cart"></i>
                                <span class="badge bg-danger rounded-pill" id="cartCount">0</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth.login') }}">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth.register') }}">Register</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Breadcrumbs -->
    {% if breadcrumbs %}
    <nav aria-label="breadcrumb" class="bg-light">
        <div class="container">
            <ol class="breadcrumb mb-0 py-2">
                {% for crumb in breadcrumbs %}
                    {% if loop.last %}
                        <li class="breadcrumb-item active" aria-current="page">{{ crumb.name }}</li>
                    {% else %}
                        <li class="breadcrumb-item"><a href="{{ crumb.url }}">{{ crumb.name }}</a></li>
                    {% endif %}
                {% endfor %}
            </ol>
        </div>
    </nav>
    {% endif %}
    
    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}
    
    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-dark text-light mt-5">
        <div class="container py-5">
            <div class="row">
                <div class="col-md-3 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-store me-2"></i>FlaskShop
                    </h5>
                    <p class="text-muted">Your trusted online shopping destination for quality products at great prices.</p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <h6 class="fw-bold mb-3">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('main.about') }}" class="text-muted text-decoration-none">About Us</a></li>
                        <li><a href="{{ url_for('main.contact') }}" class="text-muted text-decoration-none">Contact</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Privacy Policy</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Terms of Service</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Shipping Info</a></li>
                    </ul>
                </div>
                <div class="col-md-3 mb-4">
                    <h6 class="fw-bold mb-3">Categories</h6>
                    <ul class="list-unstyled">
                        {% for category in nav_categories[:5] %}
                        <li><a href="{{ url_for('shop.category', slug=category.slug) }}" class="text-muted text-decoration-none">{{ category.name }}</a></li>
                        {% endfor %}
                    </ul>
                </div>
                <div class="col-md-3 mb-4">
                    <h6 class="fw-bold mb-3">Newsletter</h6>
                    <p class="text-muted">Subscribe to get updates on new products and offers.</p>
                    <form class="newsletter-form">
                        <div class="input-group">
                            <input type="email" class="form-control" placeholder="Your email">
                            <button class="btn btn-primary" type="submit">Subscribe</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="border-top border-secondary">
            <div class="container py-3">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="text-muted mb-0">&copy; 2024 FlaskShop. All rights reserved.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <img src="{{ url_for('static', filename='images/payment-methods.png') }}" alt="Payment Methods" class="payment-methods">
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
