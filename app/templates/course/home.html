{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <h1><i class="fas fa-graduation-cap text-primary"></i> Complete Flask Course</h1>
        <p class="lead">Learn Flask from beginner to master level with hands-on examples and real-world projects.</p>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle"></i> Course Overview</h5>
            <p>This comprehensive course covers all aspects of Flask web development, including:</p>
            <ul>
                <li><strong>Flask Fundamentals</strong> - Routing, templates, static files</li>
                <li><strong>Forms & Validation</strong> - WTForms, user input handling</li>
                <li><strong>Sessions & Cookies</strong> - State management, user tracking</li>
                <li><strong>Database Integration</strong> - SQLAlchemy, models, relationships</li>
                <li><strong>Authentication</strong> - Login systems, user management</li>
                <li><strong>Advanced Topics</strong> - APIs, security, deployment</li>
            </ul>
        </div>

        <div class="alert alert-success">
            <h5><i class="fas fa-lightbulb"></i> Learn by Example</h5>
            <p>This entire e-commerce website you're browsing was built using the concepts taught in this course! 
            You can see practical implementations of:</p>
            <ul>
                <li>User authentication and sessions</li>
                <li>Database models and relationships</li>
                <li>Form handling and validation</li>
                <li>Shopping cart with session management</li>
                <li>Admin panel and CRUD operations</li>
            </ul>
        </div>

        <h3>Course Structure</h3>
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">Part 1: Fundamentals</h5>
                        <p class="card-text">Lessons 1-6: Basic Flask concepts, routing, templates</p>
                        <a href="{{ url_for('main.course_lesson', lesson_id=1) }}" class="btn btn-primary btn-sm">Start Here</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">Part 2: Forms & Input</h5>
                        <p class="card-text">Lessons 7-10: HTML forms, WTForms, validation</p>
                        <a href="{{ url_for('main.course_lesson', lesson_id=7) }}" class="btn btn-outline-primary btn-sm">View</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">Part 3: Sessions & Cookies</h5>
                        <p class="card-text">Lessons 11-14: State management, user sessions</p>
                        <a href="{{ url_for('main.course_lesson', lesson_id=11) }}" class="btn btn-outline-primary btn-sm">View</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">Part 4: Database</h5>
                        <p class="card-text">Lessons 15-18: SQLAlchemy, models, migrations</p>
                        <a href="{{ url_for('main.course_lesson', lesson_id=15) }}" class="btn btn-outline-primary btn-sm">View</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">Part 5: Authentication</h5>
                        <p class="card-text">Lessons 19-23: User login, registration, security</p>
                        <a href="{{ url_for('main.course_lesson', lesson_id=20) }}" class="btn btn-outline-primary btn-sm">View</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">Part 6: Advanced</h5>
                        <p class="card-text">Lessons 24-30: Blueprints, APIs, deployment</p>
                        <a href="{{ url_for('main.course_lesson', lesson_id=24) }}" class="btn btn-outline-primary btn-sm">View</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5>Quick Navigation</h5>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('main.course_lessons') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-list"></i> All Lessons
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=1) }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-play"></i> Start Course
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=11) }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-cookie-bite"></i> Sessions & Cookies
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=20) }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-lock"></i> Login Systems
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-body">
                <h6>Course Files</h6>
                <p class="small text-muted">Complete lesson content with examples and exercises available in:</p>
                <code class="small">flask-course/lessons/</code>
                <hr>
                <a href="#" class="btn btn-sm btn-outline-secondary w-100" onclick="alert('Course files are in the flask-course directory')">
                    <i class="fas fa-folder"></i> Browse Course Files
                </a>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-body">
                <h6>Course Progress</h6>
                <div class="progress mb-2">
                    <div class="progress-bar" style="width: 0%"></div>
                </div>
                <small class="text-muted">0 of 30 lessons completed</small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
