{% extends "base.html" %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('main.course_home') }}">Course</a></li>
        <li class="breadcrumb-item"><a href="{{ url_for('main.course_lessons') }}">Lessons</a></li>
        <li class="breadcrumb-item active">Lesson {{ lesson_id }}</li>
    </ol>
</nav>

<div class="row">
    <div class="col-md-9">
        <h1>Lesson {{ lesson_id }}: Flask Course Content</h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle"></i> Lesson Information</h5>
            <p>This lesson is part of the comprehensive Flask course. The complete content for this lesson is available in the course files.</p>
        </div>

        {% if lesson_id == 1 %}
        <div class="card">
            <div class="card-header">
                <h5>Lesson 1: Introduction to Flask</h5>
            </div>
            <div class="card-body">
                <h6>Learning Objectives:</h6>
                <ul>
                    <li>Understand what Flask is and why it's popular</li>
                    <li>Know the advantages and disadvantages of Flask</li>
                    <li>Set up your development environment</li>
                    <li>Understand Flask's philosophy and architecture</li>
                </ul>
                
                <h6>What is Flask?</h6>
                <p>Flask is a lightweight and flexible Python web framework that provides the basic tools and libraries to build web applications. It's called a "micro" framework because it doesn't require particular tools or libraries and keeps the core simple but extensible.</p>
                
                <h6>Key Characteristics:</h6>
                <ul>
                    <li><strong>Minimalist:</strong> Provides only the essentials</li>
                    <li><strong>Flexible:</strong> Doesn't make many decisions for you</li>
                    <li><strong>Extensible:</strong> Easy to add functionality through extensions</li>
                    <li><strong>Pythonic:</strong> Follows Python conventions and idioms</li>
                </ul>
            </div>
        </div>
        
        {% elif lesson_id == 11 %}
        <div class="card">
            <div class="card-header">
                <h5>Lesson 11: Understanding HTTP Sessions</h5>
            </div>
            <div class="card-body">
                <h6>Learning Objectives:</h6>
                <ul>
                    <li>Understand what HTTP sessions are and why they're needed</li>
                    <li>Learn the difference between stateless and stateful protocols</li>
                    <li>Understand various session storage mechanisms</li>
                    <li>Know when and how to use sessions effectively</li>
                </ul>
                
                <h6>HTTP: A Stateless Protocol</h6>
                <p>HTTP (HyperText Transfer Protocol) is <strong>stateless</strong>, meaning each request is independent and the server doesn't remember previous requests.</p>
                
                <div class="alert alert-warning">
                    <h6>Example of Stateless Behavior:</h6>
                    <pre>Request 1: GET /login → Server responds with login form
Request 2: POST /login → Server authenticates user  
Request 3: GET /profile → Server doesn't know user is logged in!</pre>
                </div>
                
                <h6>What Are Sessions?</h6>
                <p><strong>Sessions</strong> are a way to store information about a user across multiple HTTP requests. They create the illusion of a persistent connection between the client and server.</p>
            </div>
        </div>
        
        {% elif lesson_id == 12 %}
        <div class="card">
            <div class="card-header">
                <h5>Lesson 12: Flask Sessions</h5>
            </div>
            <div class="card-body">
                <h6>Learning Objectives:</h6>
                <ul>
                    <li>Implement sessions in Flask applications</li>
                    <li>Store and retrieve session data</li>
                    <li>Configure session settings</li>
                    <li>Handle session security</li>
                </ul>
                
                <h6>Setting Up Flask Sessions</h6>
                <p>Flask provides built-in session support that's easy to use and secure.</p>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-code"></i> Code Example</h6>
                    </div>
                    <div class="card-body">
                        <pre><code>from flask import Flask, session, request

app = Flask(__name__)
app.secret_key = 'your-very-secret-key-here'

@app.route('/')
def home():
    if 'username' in session:
        return f'Hello, {session["username"]}!'
    return 'You are not logged in'

@app.route('/login', methods=['POST'])
def login():
    username = request.form['username']
    session['username'] = username
    session['logged_in'] = True
    return redirect(url_for('home'))</code></pre>
                    </div>
                </div>
            </div>
        </div>
        
        {% elif lesson_id == 20 %}
        <div class="card">
            <div class="card-header">
                <h5>Lesson 20: Login System</h5>
            </div>
            <div class="card-body">
                <h6>Learning Objectives:</h6>
                <ul>
                    <li>Build a complete login system</li>
                    <li>Implement user registration</li>
                    <li>Handle password security</li>
                    <li>Manage user sessions</li>
                </ul>
                
                <h6>Real-World Example</h6>
                <p>You can see a complete login system implementation in this very website! Check out:</p>
                <ul>
                    <li><a href="{{ url_for('auth.login') }}">Login Page</a> - See the login form in action</li>
                    <li><a href="{{ url_for('auth.register') }}">Registration Page</a> - User registration with validation</li>
                    <li>Session management for cart and user preferences</li>
                    <li>Password hashing and security</li>
                </ul>
                
                <div class="alert alert-success">
                    <h6><i class="fas fa-lightbulb"></i> Try It Out!</h6>
                    <p>You can test the login system with these demo accounts:</p>
                    <ul>
                        <li><strong>Admin:</strong> <EMAIL> / admin123</li>
                        <li><strong>User:</strong> <EMAIL> / password123</li>
                    </ul>
                </div>
            </div>
        </div>
        
        {% else %}
        <div class="card">
            <div class="card-header">
                <h5>Lesson {{ lesson_id }}: Course Content</h5>
            </div>
            <div class="card-body">
                <p>This lesson is part of the comprehensive Flask course. The detailed content for this lesson is available in the course files.</p>
                
                <div class="alert alert-info">
                    <h6><i class="fas fa-folder-open"></i> Course Files Location</h6>
                    <p>The complete lesson content can be found in:</p>
                    <code>flask-course/lessons/{{ "%02d"|format(lesson_id) }}-*/README.md</code>
                </div>
                
                <h6>What You'll Learn:</h6>
                <ul>
                    <li>Core concepts for this lesson</li>
                    <li>Practical implementation examples</li>
                    <li>Best practices and common patterns</li>
                    <li>Hands-on exercises</li>
                </ul>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-code"></i> Example Code Structure</h6>
                    </div>
                    <div class="card-body">
                        <pre><code># Example code for lesson {{ lesson_id }}
from flask import Flask

app = Flask(__name__)

@app.route('/')
def lesson_{{ lesson_id }}_example():
    return 'Lesson {{ lesson_id }} example'

if __name__ == '__main__':
    app.run(debug=True)</code></pre>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="card mt-4 border-warning">
            <div class="card-header bg-warning">
                <h6><i class="fas fa-dumbbell"></i> Exercise</h6>
            </div>
            <div class="card-body">
                <p><strong>Exercise for Lesson {{ lesson_id }}:</strong></p>
                <ol>
                    <li>Review the lesson content in the course files</li>
                    <li>Try the code examples provided</li>
                    <li>Complete the practical exercises</li>
                    <li>Experiment with variations of the examples</li>
                </ol>
                <div class="alert alert-info">
                    <strong>Note:</strong> Complete lesson content with detailed exercises is available in the <code>flask-course/</code> directory.
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card">
            <div class="card-body">
                <h6>Navigation</h6>
                {% if lesson_id > 1 %}
                <a href="{{ url_for('main.course_lesson', lesson_id=lesson_id-1) }}" class="btn btn-outline-primary btn-sm w-100 mb-2">
                    <i class="fas fa-arrow-left"></i> Previous Lesson
                </a>
                {% endif %}
                
                {% if lesson_id < 30 %}
                <a href="{{ url_for('main.course_lesson', lesson_id=lesson_id+1) }}" class="btn btn-primary btn-sm w-100 mb-2">
                    Next Lesson <i class="fas fa-arrow-right"></i>
                </a>
                {% endif %}
                
                <a href="{{ url_for('main.course_lessons') }}" class="btn btn-outline-secondary btn-sm w-100">
                    <i class="fas fa-list"></i> All Lessons
                </a>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-body">
                <h6>Quick Links</h6>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('main.course_lesson', lesson_id=1) }}" class="list-group-item list-group-item-action list-group-item-sm">
                        Lesson 1: Introduction
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=11) }}" class="list-group-item list-group-item-action list-group-item-sm">
                        Lesson 11: Sessions
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=12) }}" class="list-group-item list-group-item-action list-group-item-sm">
                        Lesson 12: Flask Sessions
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=20) }}" class="list-group-item list-group-item-action list-group-item-sm">
                        Lesson 20: Login System
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-body">
                <h6>Course Files</h6>
                <p class="small text-muted">Complete lesson content available in:</p>
                <code class="small">flask-course/lessons/</code>
                <hr>
                <div class="d-grid">
                    <button class="btn btn-sm btn-outline-secondary" onclick="alert('Course files are in the flask-course directory on your system')">
                        <i class="fas fa-folder"></i> Browse Files
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
