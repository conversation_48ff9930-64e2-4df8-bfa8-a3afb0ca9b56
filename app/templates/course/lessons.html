{% extends "base.html" %}

{% block content %}
<h1><i class="fas fa-list"></i> All Flask Course Lessons</h1>
<p class="lead">Complete curriculum from beginner to advanced Flask development.</p>

<div class="alert alert-info">
    <h5><i class="fas fa-info-circle"></i> Course Structure</h5>
    <p>The course is organized into 6 parts with 30 comprehensive lessons. Each lesson includes theory, practical examples, and hands-on exercises.</p>
</div>

<div class="row">
    <!-- Part 1: Flask Fundamentals -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Part 1: Flask Fundamentals</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Learn the basics of Flask web development</p>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('main.course_lesson', lesson_id=1) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 1: Introduction to Flask</span>
                        <span class="badge bg-secondary">30 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=2) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 2: Your First Flask App</span>
                        <span class="badge bg-secondary">45 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=3) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 3: Routing and URL Building</span>
                        <span class="badge bg-secondary">40 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=4) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 4: Templates with Jinja2</span>
                        <span class="badge bg-secondary">50 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=5) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 5: Static Files</span>
                        <span class="badge bg-secondary">25 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=6) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 6: HTTP Methods</span>
                        <span class="badge bg-secondary">35 min</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Part 2: Forms and User Input -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">Part 2: Forms and User Input</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Handle user input with forms and validation</p>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('main.course_lesson', lesson_id=7) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 7: HTML Forms</span>
                        <span class="badge bg-secondary">40 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=8) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 8: WTForms Introduction</span>
                        <span class="badge bg-secondary">45 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=9) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 9: Form Validation</span>
                        <span class="badge bg-secondary">50 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=10) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 10: File Uploads</span>
                        <span class="badge bg-secondary">35 min</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Part 3: Sessions and Cookies -->
    <div class="col-md-6 mb-4">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">Part 3: Sessions and Cookies</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Manage user state and sessions</p>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('main.course_lesson', lesson_id=11) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 11: Understanding HTTP Sessions</span>
                        <span class="badge bg-secondary">45 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=12) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 12: Flask Sessions</span>
                        <span class="badge bg-secondary">50 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=13) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 13: Cookies in Flask</span>
                        <span class="badge bg-secondary">40 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=14) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 14: Session Management</span>
                        <span class="badge bg-secondary">45 min</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Part 4: Database Integration -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Part 4: Database Integration</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Work with databases using SQLAlchemy</p>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('main.course_lesson', lesson_id=15) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 15: Database Basics</span>
                        <span class="badge bg-secondary">40 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=16) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 16: SQLAlchemy Introduction</span>
                        <span class="badge bg-secondary">55 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=17) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 17: Models and Relationships</span>
                        <span class="badge bg-secondary">60 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=18) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 18: Database Migrations</span>
                        <span class="badge bg-secondary">45 min</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Part 5: User Authentication -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">Part 5: User Authentication</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Implement secure user authentication</p>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('main.course_lesson', lesson_id=19) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 19: User Registration</span>
                        <span class="badge bg-secondary">50 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=20) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 20: Login System</span>
                        <span class="badge bg-secondary">55 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=21) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 21: Flask-Login</span>
                        <span class="badge bg-secondary">45 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=22) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 22: Password Security</span>
                        <span class="badge bg-secondary">40 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=23) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 23: User Roles</span>
                        <span class="badge bg-secondary">50 min</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Part 6: Advanced Flask -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5 class="mb-0">Part 6: Advanced Flask</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Advanced topics and best practices</p>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('main.course_lesson', lesson_id=24) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 24: Blueprints</span>
                        <span class="badge bg-secondary">55 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=25) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 25: Error Handling</span>
                        <span class="badge bg-secondary">40 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=26) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 26: Logging and Debugging</span>
                        <span class="badge bg-secondary">45 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=27) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 27: Testing</span>
                        <span class="badge bg-secondary">60 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=28) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 28: RESTful APIs</span>
                        <span class="badge bg-secondary">65 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=29) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 29: Security</span>
                        <span class="badge bg-secondary">50 min</span>
                    </a>
                    <a href="{{ url_for('main.course_lesson', lesson_id=30) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-book-open me-2"></i>Lesson 30: Deployment</span>
                        <span class="badge bg-secondary">55 min</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="alert alert-success mt-4">
    <h5><i class="fas fa-folder-open"></i> Complete Course Materials</h5>
    <p>All lesson content, code examples, and exercises are available in the <code>flask-course/</code> directory. 
    Each lesson includes detailed explanations, practical examples, and hands-on exercises.</p>
</div>
{% endblock %}
