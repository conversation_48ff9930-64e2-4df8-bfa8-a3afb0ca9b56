{% extends "base.html" %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <h2 class="fw-bold">Sign In</h2>
                        <p class="text-muted">Welcome back! Please sign in to your account.</p>
                    </div>
                    
                    <form method="POST" class="needs-validation" novalidate>
                        {{ form.hidden_tag() }}
                        
                        <!-- Username/Email Field -->
                        <div class="form-floating mb-3">
                            {{ form.username_or_email(class="form-control", placeholder="Username or Email") }}
                            {{ form.username_or_email.label(class="form-label") }}
                            {% if form.username_or_email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.username_or_email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Password Field -->
                        <div class="form-floating mb-3">
                            {{ form.password(class="form-control", placeholder="Password") }}
                            {{ form.password.label(class="form-label") }}
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Remember Me -->
                        <div class="form-check mb-3">
                            {{ form.remember_me(class="form-check-input") }}
                            {{ form.remember_me.label(class="form-check-label") }}
                        </div>
                        
                        <!-- Submit Button -->
                        <div class="d-grid">
                            {{ form.submit(class="btn btn-primary btn-lg") }}
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-0">Don't have an account? 
                            <a href="{{ url_for('auth.register') }}" class="text-decoration-none">Sign up here</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
