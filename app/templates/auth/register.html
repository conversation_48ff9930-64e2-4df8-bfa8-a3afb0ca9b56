{% extends "base.html" %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <h2 class="fw-bold">Create Account</h2>
                        <p class="text-muted">Join us today and start shopping!</p>
                    </div>
                    
                    <form method="POST" class="needs-validation" novalidate>
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <!-- First Name -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    {{ form.first_name(class="form-control", placeholder="First Name") }}
                                    {{ form.first_name.label(class="form-label") }}
                                    {% if form.first_name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.first_name.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- Last Name -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    {{ form.last_name(class="form-control", placeholder="Last Name") }}
                                    {{ form.last_name.label(class="form-label") }}
                                    {% if form.last_name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.last_name.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Username -->
                        <div class="form-floating mb-3">
                            {{ form.username(class="form-control", placeholder="Username") }}
                            {{ form.username.label(class="form-label") }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Email -->
                        <div class="form-floating mb-3">
                            {{ form.email(class="form-control", placeholder="Email") }}
                            {{ form.email.label(class="form-label") }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Phone -->
                        <div class="form-floating mb-3">
                            {{ form.phone(class="form-control", placeholder="Phone Number") }}
                            {{ form.phone.label(class="form-label") }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.phone.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Password -->
                        <div class="form-floating mb-3">
                            {{ form.password(class="form-control", placeholder="Password") }}
                            {{ form.password.label(class="form-label") }}
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Confirm Password -->
                        <div class="form-floating mb-3">
                            {{ form.password2(class="form-control", placeholder="Confirm Password") }}
                            {{ form.password2.label(class="form-label") }}
                            {% if form.password2.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password2.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Password Requirements -->
                        <div class="alert alert-info">
                            <small>
                                <strong>Password Requirements:</strong><br>
                                • At least 8 characters long<br>
                                • Contains uppercase and lowercase letters<br>
                                • Contains at least one number
                            </small>
                        </div>
                        
                        <!-- Submit Button -->
                        <div class="d-grid">
                            {{ form.submit(class="btn btn-primary btn-lg") }}
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-0">Already have an account? 
                            <a href="{{ url_for('auth.login') }}" class="text-decoration-none">Sign in here</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
