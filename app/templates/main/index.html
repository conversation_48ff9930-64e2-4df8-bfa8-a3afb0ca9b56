{% extends "base.html" %}

{% block meta_description %}{{ config.SITE_DESCRIPTION }}{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">Welcome to FlaskShop</h1>
                <p class="lead mb-4">Discover amazing products at unbeatable prices. Quality guaranteed, fast shipping, and excellent customer service.</p>
                <div class="d-flex gap-3">
                    <a href="{{ url_for('shop.products') }}" class="btn btn-light btn-lg">Shop Now</a>
                    <a href="{{ url_for('main.about') }}" class="btn btn-outline-light btn-lg">Learn More</a>
                </div>
            </div>
            <div class="col-lg-6">
                <img src="{{ url_for('static', filename='images/hero-image.jpg') }}" alt="Shopping" class="img-fluid rounded">
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section py-5">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-3 mb-4">
                <div class="feature-item">
                    <i class="fas fa-shipping-fast fa-3x text-primary mb-3"></i>
                    <h5>Fast Shipping</h5>
                    <p class="text-muted">Free shipping on orders over $50</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="feature-item">
                    <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                    <h5>Secure Payment</h5>
                    <p class="text-muted">Your payment information is safe</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="feature-item">
                    <i class="fas fa-undo fa-3x text-primary mb-3"></i>
                    <h5>Easy Returns</h5>
                    <p class="text-muted">30-day return policy</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="feature-item">
                    <i class="fas fa-headset fa-3x text-primary mb-3"></i>
                    <h5>24/7 Support</h5>
                    <p class="text-muted">Customer support available anytime</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
{% if categories %}
<section class="categories-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="fw-bold">Shop by Category</h2>
                <p class="text-muted">Browse our wide selection of products</p>
            </div>
        </div>
        <div class="row">
            {% for category in categories %}
            <div class="col-lg-2 col-md-4 col-6 mb-4">
                <a href="{{ url_for('shop.category', slug=category.slug) }}" class="text-decoration-none">
                    <div class="card category-card h-100 text-center">
                        {% if category.image %}
                        <img src="{{ url_for('static', filename='uploads/categories/' + category.image) }}" 
                             class="card-img-top" alt="{{ category.name }}" style="height: 120px; object-fit: cover;">
                        {% else %}
                        <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 120px;">
                            <i class="fas fa-folder fa-3x text-muted"></i>
                        </div>
                        {% endif %}
                        <div class="card-body">
                            <h6 class="card-title">{{ category.name }}</h6>
                            <small class="text-muted">{{ category.get_product_count() }} products</small>
                        </div>
                    </div>
                </a>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Featured Products -->
{% if featured_products %}
<section class="featured-products py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="fw-bold">Featured Products</h2>
                <p class="text-muted">Check out our handpicked featured items</p>
            </div>
        </div>
        <div class="row">
            {% for product in featured_products %}
            <div class="col-lg-3 col-md-6 mb-4">
                {% include 'components/product_card.html' %}
            </div>
            {% endfor %}
        </div>
        <div class="text-center">
            <a href="{{ url_for('shop.products', sort='featured') }}" class="btn btn-primary btn-lg">View All Featured</a>
        </div>
    </div>
</section>
{% endif %}

<!-- Latest Products -->
{% if latest_products %}
<section class="latest-products py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="fw-bold">Latest Products</h2>
                <p class="text-muted">Discover our newest arrivals</p>
            </div>
        </div>
        <div class="row">
            {% for product in latest_products %}
            <div class="col-lg-3 col-md-6 mb-4">
                {% include 'components/product_card.html' %}
            </div>
            {% endfor %}
        </div>
        <div class="text-center">
            <a href="{{ url_for('shop.products') }}" class="btn btn-primary btn-lg">View All Products</a>
        </div>
    </div>
</section>
{% endif %}

<!-- Newsletter Section -->
<section class="newsletter-section py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h3 class="fw-bold mb-3">Stay Updated</h3>
                <p class="mb-0">Subscribe to our newsletter and get the latest updates on new products and exclusive offers.</p>
            </div>
            <div class="col-lg-6">
                <form class="newsletter-form d-flex gap-2">
                    <input type="email" class="form-control form-control-lg" placeholder="Enter your email" required>
                    <button type="submit" class="btn btn-light btn-lg">Subscribe</button>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="testimonials-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="fw-bold">What Our Customers Say</h2>
                <p class="text-muted">Read reviews from our satisfied customers</p>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <p class="card-text">"Excellent service and fast delivery. The products are exactly as described and the quality is outstanding."</p>
                        <footer class="blockquote-footer">
                            <strong>Sarah Johnson</strong>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <p class="card-text">"Great shopping experience! Easy to navigate website and the customer support team is very helpful."</p>
                        <footer class="blockquote-footer">
                            <strong>Mike Chen</strong>
                        </footer>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                            <i class="fas fa-star text-warning"></i>
                        </div>
                        <p class="card-text">"I've been shopping here for months and I'm always impressed with the quality and service. Highly recommended!"</p>
                        <footer class="blockquote-footer">
                            <strong>Emily Davis</strong>
                        </footer>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block structured_data %}
{{ super() }}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Store",
    "name": "{{ config.SITE_NAME }}",
    "description": "{{ config.SITE_DESCRIPTION }}",
    "url": "{{ config.SITE_URL }}",
    "logo": "{{ url_for('static', filename='images/logo.png', _external=True) }}",
    "address": {
        "@type": "PostalAddress",
        "addressCountry": "US"
    },
    "priceRange": "$",
    "paymentAccepted": ["Credit Card", "PayPal", "Bank Transfer"],
    "currenciesAccepted": "USD"
}
</script>
{% endblock %}
