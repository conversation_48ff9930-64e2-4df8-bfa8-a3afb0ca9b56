<!-- Product Card Component -->
<div class="card product-card h-100 position-relative">
    <!-- Discount Badge -->
    {% if product.discount_percentage > 0 %}
    <span class="product-discount">-{{ product.discount_percentage }}%</span>
    {% endif %}
    
    <!-- Wishlist Button -->
    {% if current_user.is_authenticated %}
    <button class="wishlist-btn" data-product-id="{{ product.id }}" title="Add to Wishlist">
        <i class="far fa-heart"></i>
    </button>
    {% endif %}
    
    <!-- Product Image -->
    <a href="{{ url_for('shop.product_detail', slug=product.slug) }}">
        <img src="{{ url_for('static', filename='uploads/products/' + product.main_image) }}" 
             class="card-img-top" alt="{{ product.name }}" loading="lazy">
    </a>
    
    <div class="card-body d-flex flex-column">
        <!-- Product Category -->
        <small class="text-muted mb-1">{{ product.category.name }}</small>
        
        <!-- Product Name -->
        <h6 class="card-title">
            <a href="{{ url_for('shop.product_detail', slug=product.slug) }}" 
               class="text-decoration-none text-dark">{{ product.name }}</a>
        </h6>
        
        <!-- Product Rating -->
        {% set avg_rating = product.get_average_rating() %}
        {% set review_count = product.get_review_count() %}
        {% if avg_rating > 0 %}
        <div class="product-rating mb-2">
            {% for i in range(1, 6) %}
                {% if i <= avg_rating %}
                    <i class="fas fa-star"></i>
                {% elif i - 0.5 <= avg_rating %}
                    <i class="fas fa-star-half-alt"></i>
                {% else %}
                    <i class="far fa-star"></i>
                {% endif %}
            {% endfor %}
            <small class="text-muted ms-1">({{ review_count }})</small>
        </div>
        {% endif %}
        
        <!-- Product Price -->
        <div class="product-pricing mb-3">
            <span class="product-price">${{ "%.2f"|format(product.price) }}</span>
            {% if product.compare_price and product.compare_price > product.price %}
            <span class="product-compare-price ms-2">${{ "%.2f"|format(product.compare_price) }}</span>
            {% endif %}
        </div>
        
        <!-- Stock Status -->
        <div class="product-stock-status mb-3">
            {% if product.is_in_stock %}
                {% if product.is_low_stock %}
                <small class="stock-low">
                    <i class="fas fa-exclamation-triangle"></i> Only {{ product.stock_quantity }} left
                </small>
                {% else %}
                <small class="stock-in">
                    <i class="fas fa-check-circle"></i> In Stock
                </small>
                {% endif %}
            {% else %}
            <small class="stock-out">
                <i class="fas fa-times-circle"></i> Out of Stock
            </small>
            {% endif %}
        </div>
        
        <!-- Add to Cart Button -->
        <div class="mt-auto">
            {% if product.is_in_stock %}
            <button class="btn btn-primary w-100 add-to-cart-btn" data-product-id="{{ product.id }}">
                <i class="fas fa-shopping-cart me-2"></i>Add to Cart
            </button>
            {% else %}
            <button class="btn btn-secondary w-100" disabled>
                <i class="fas fa-times me-2"></i>Out of Stock
            </button>
            {% endif %}
        </div>
    </div>
</div>
