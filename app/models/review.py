from datetime import datetime
from app.extensions import db

class Review(db.Model):
    __tablename__ = 'reviews'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    rating = db.Column(db.Integer, nullable=False)  # 1-5 stars
    title = db.<PERSON>umn(db.String(200))
    comment = db.Column(db.Text)
    
    # Review status
    is_approved = db.Column(db.<PERSON>, default=False)
    is_verified_purchase = db.Column(db.<PERSON>, default=False)
    
    # Helpful votes
    helpful_count = db.Column(db.Integer, default=0)
    not_helpful_count = db.Column(db.Integer, default=0)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Foreign keys
    user_id = db.<PERSON>umn(db.<PERSON><PERSON>, db.<PERSON>('users.id'), nullable=False)
    product_id = db.Column(db.Integer, db.<PERSON><PERSON>ey('products.id'), nullable=False)
    
    # Unique constraint to prevent multiple reviews per user per product
    __table_args__ = (db.UniqueConstraint('user_id', 'product_id', name='unique_user_product_review'),)
    
    def get_helpful_percentage(self):
        """Calculate helpful percentage"""
        total_votes = self.helpful_count + self.not_helpful_count
        if total_votes == 0:
            return 0
        return (self.helpful_count / total_votes) * 100
    
    def to_dict(self):
        """Convert review to dictionary"""
        return {
            'id': self.id,
            'rating': self.rating,
            'title': self.title,
            'comment': self.comment,
            'user_name': self.user.first_name,  # Only first name for privacy
            'is_verified_purchase': self.is_verified_purchase,
            'helpful_count': self.helpful_count,
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        return f'<Review {self.rating} stars for {self.product.name}>'
