from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from app import db
import uuid

class Order(db.Model):
    __tablename__ = 'orders'
    
    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(50), unique=True, nullable=False, index=True)
    
    # Order status
    status = db.Column(db.String(50), default='pending')  # pending, confirmed, processing, shipped, delivered, cancelled, refunded
    payment_status = db.Column(db.String(50), default='pending')  # pending, paid, failed, refunded
    
    # Amounts
    subtotal = db.Column(db.Numeric(10, 2), nullable=False)
    tax_amount = db.Column(db.Numeric(10, 2), default=0)
    shipping_amount = db.Column(db.Numeric(10, 2), default=0)
    discount_amount = db.Column(db.Numeric(10, 2), default=0)
    total_amount = db.Column(db.Numeric(10, 2), nullable=False)
    
    # Customer information
    customer_email = db.Column(db.String(120), nullable=False)
    customer_phone = db.Column(db.String(20))
    
    # Billing address
    billing_first_name = db.Column(db.String(50), nullable=False)
    billing_last_name = db.Column(db.String(50), nullable=False)
    billing_address_line1 = db.Column(db.String(200), nullable=False)
    billing_address_line2 = db.Column(db.String(200))
    billing_city = db.Column(db.String(100), nullable=False)
    billing_state = db.Column(db.String(100), nullable=False)
    billing_postal_code = db.Column(db.String(20), nullable=False)
    billing_country = db.Column(db.String(100), nullable=False)
    
    # Shipping address
    shipping_first_name = db.Column(db.String(50), nullable=False)
    shipping_last_name = db.Column(db.String(50), nullable=False)
    shipping_address_line1 = db.Column(db.String(200), nullable=False)
    shipping_address_line2 = db.Column(db.String(200))
    shipping_city = db.Column(db.String(100), nullable=False)
    shipping_state = db.Column(db.String(100), nullable=False)
    shipping_postal_code = db.Column(db.String(20), nullable=False)
    shipping_country = db.Column(db.String(100), nullable=False)
    
    # Shipping information
    shipping_method = db.Column(db.String(100))
    tracking_number = db.Column(db.String(100))
    estimated_delivery = db.Column(db.Date)
    
    # Payment information
    payment_method = db.Column(db.String(50))  # credit_card, paypal, bank_transfer, cash_on_delivery
    payment_reference = db.Column(db.String(100))
    
    # Notes
    notes = db.Column(db.Text)
    admin_notes = db.Column(db.Text)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    shipped_at = db.Column(db.DateTime)
    delivered_at = db.Column(db.DateTime)
    
    # Foreign keys
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    coupon_id = db.Column(db.Integer, db.ForeignKey('coupons.id'))
    
    # Relationships
    items = db.relationship('OrderItem', backref='order', lazy='dynamic', cascade='all, delete-orphan')
    coupon = db.relationship('Coupon', backref='orders')
    
    def __init__(self, **kwargs):
        super(Order, self).__init__(**kwargs)
        if not self.order_number:
            self.order_number = self.generate_order_number()
    
    @staticmethod
    def generate_order_number():
        """Generate unique order number"""
        timestamp = datetime.utcnow().strftime('%Y%m%d')
        unique_id = str(uuid.uuid4().hex)[:8].upper()
        return f"ORD-{timestamp}-{unique_id}"
    
    @property
    def billing_full_name(self):
        return f"{self.billing_first_name} {self.billing_last_name}"
    
    @property
    def shipping_full_name(self):
        return f"{self.shipping_first_name} {self.shipping_last_name}"
    
    @property
    def billing_full_address(self):
        address_parts = [
            self.billing_address_line1,
            self.billing_address_line2,
            self.billing_city,
            self.billing_state,
            self.billing_postal_code,
            self.billing_country
        ]
        return ', '.join([part for part in address_parts if part])
    
    @property
    def shipping_full_address(self):
        address_parts = [
            self.shipping_address_line1,
            self.shipping_address_line2,
            self.shipping_city,
            self.shipping_state,
            self.shipping_postal_code,
            self.shipping_country
        ]
        return ', '.join([part for part in address_parts if part])
    
    def get_total_items(self):
        """Get total number of items in order"""
        return sum(item.quantity for item in self.items)
    
    def can_be_cancelled(self):
        """Check if order can be cancelled"""
        return self.status in ['pending', 'confirmed']
    
    def can_be_refunded(self):
        """Check if order can be refunded"""
        return self.status in ['delivered'] and self.payment_status == 'paid'
    
    def update_status(self, new_status):
        """Update order status with timestamp tracking"""
        self.status = new_status
        self.updated_at = datetime.utcnow()
        
        if new_status == 'shipped':
            self.shipped_at = datetime.utcnow()
        elif new_status == 'delivered':
            self.delivered_at = datetime.utcnow()
    
    def to_dict(self):
        """Convert order to dictionary"""
        return {
            'id': self.id,
            'order_number': self.order_number,
            'status': self.status,
            'payment_status': self.payment_status,
            'total_amount': float(self.total_amount),
            'created_at': self.created_at.isoformat(),
            'total_items': self.get_total_items()
        }
    
    def __repr__(self):
        return f'<Order {self.order_number}>'

class OrderItem(db.Model):
    __tablename__ = 'order_items'
    
    id = db.Column(db.Integer, primary_key=True)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Numeric(10, 2), nullable=False)  # Price at time of order
    total_price = db.Column(db.Numeric(10, 2), nullable=False)
    
    # Product information at time of order (in case product is deleted/modified)
    product_name = db.Column(db.String(200), nullable=False)
    product_sku = db.Column(db.String(100), nullable=False)
    product_image = db.Column(db.String(200))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Foreign keys
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    
    def to_dict(self):
        """Convert order item to dictionary"""
        return {
            'id': self.id,
            'product_name': self.product_name,
            'product_sku': self.product_sku,
            'quantity': self.quantity,
            'unit_price': float(self.unit_price),
            'total_price': float(self.total_price),
            'product_image': self.product_image
        }
    
    def __repr__(self):
        return f'<OrderItem {self.product_name} x{self.quantity}>'
