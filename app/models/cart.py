from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from app import db

class Cart(db.Model):
    __tablename__ = 'carts'
    
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.String(100))  # For guest users
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Foreign keys
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # Relationships
    items = db.relationship('CartItem', backref='cart', lazy='dynamic', cascade='all, delete-orphan')
    
    def get_total_items(self):
        """Get total number of items in cart"""
        return sum(item.quantity for item in self.items)
    
    def get_subtotal(self):
        """Calculate cart subtotal"""
        return sum(item.get_total_price() for item in self.items)
    
    def get_total_weight(self):
        """Calculate total weight of cart items"""
        total_weight = 0
        for item in self.items:
            if item.product.weight:
                total_weight += float(item.product.weight) * item.quantity
        return total_weight
    
    def add_item(self, product, quantity=1):
        """Add item to cart or update quantity if exists"""
        existing_item = self.items.filter_by(product_id=product.id).first()
        
        if existing_item:
            existing_item.quantity += quantity
            if existing_item.quantity <= 0:
                db.session.delete(existing_item)
        else:
            if quantity > 0:
                new_item = CartItem(cart=self, product=product, quantity=quantity)
                db.session.add(new_item)
        
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    def remove_item(self, product_id):
        """Remove item from cart"""
        item = self.items.filter_by(product_id=product_id).first()
        if item:
            db.session.delete(item)
            self.updated_at = datetime.utcnow()
            db.session.commit()
    
    def update_item_quantity(self, product_id, quantity):
        """Update item quantity"""
        item = self.items.filter_by(product_id=product_id).first()
        if item:
            if quantity <= 0:
                db.session.delete(item)
            else:
                item.quantity = quantity
            self.updated_at = datetime.utcnow()
            db.session.commit()
    
    def clear(self):
        """Clear all items from cart"""
        for item in self.items:
            db.session.delete(item)
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    def to_dict(self):
        """Convert cart to dictionary"""
        return {
            'id': self.id,
            'total_items': self.get_total_items(),
            'subtotal': float(self.get_subtotal()),
            'items': [item.to_dict() for item in self.items]
        }
    
    def __repr__(self):
        return f'<Cart {self.id}>'

class CartItem(db.Model):
    __tablename__ = 'cart_items'
    
    id = db.Column(db.Integer, primary_key=True)
    quantity = db.Column(db.Integer, nullable=False, default=1)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Foreign keys
    cart_id = db.Column(db.Integer, db.ForeignKey('carts.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    
    # Unique constraint to prevent duplicate items
    __table_args__ = (db.UniqueConstraint('cart_id', 'product_id', name='unique_cart_product'),)
    
    def get_total_price(self):
        """Calculate total price for this cart item"""
        return self.product.price * self.quantity
    
    def to_dict(self):
        """Convert cart item to dictionary"""
        return {
            'id': self.id,
            'product_id': self.product_id,
            'product_name': self.product.name,
            'product_price': float(self.product.price),
            'product_image': self.product.main_image,
            'quantity': self.quantity,
            'total_price': float(self.get_total_price())
        }
    
    def __repr__(self):
        return f'<CartItem {self.product.name} x{self.quantity}>'
