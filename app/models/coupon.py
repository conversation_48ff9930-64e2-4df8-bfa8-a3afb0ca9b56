from datetime import datetime, date
from app.extensions import db

class Coupon(db.Model):
    __tablename__ = 'coupons'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(50), unique=True, nullable=False, index=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    
    # Discount configuration
    discount_type = db.Column(db.String(20), nullable=False)  # 'percentage' or 'fixed'
    discount_value = db.Column(db.Numeric(10, 2), nullable=False)
    
    # Usage limits
    usage_limit = db.Column(db.Integer)  # Total usage limit (null = unlimited)
    usage_limit_per_user = db.Column(db.Integer, default=1)  # Per user limit
    used_count = db.Column(db.Integer, default=0)
    
    # Minimum requirements
    minimum_amount = db.Column(db.Numeric(10, 2))  # Minimum order amount
    maximum_discount = db.Column(db.Numeric(10, 2))  # Maximum discount amount (for percentage coupons)
    
    # Validity period
    valid_from = db.Column(db.Date, nullable=False)
    valid_until = db.Column(db.Date, nullable=False)
    
    # Status
    is_active = db.Column(db.Boolean, default=True)
    
    # Applicable products/categories (for future enhancement)
    applicable_to = db.Column(db.String(20), default='all')  # 'all', 'categories', 'products'
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def is_valid(self, user_id=None, order_amount=0):
        """Check if coupon is valid for use"""
        today = date.today()
        
        # Check if coupon is active
        if not self.is_active:
            return False, "Coupon is not active"
        
        # Check validity period
        if today < self.valid_from:
            return False, "Coupon is not yet valid"
        
        if today > self.valid_until:
            return False, "Coupon has expired"
        
        # Check usage limit
        if self.usage_limit and self.used_count >= self.usage_limit:
            return False, "Coupon usage limit exceeded"
        
        # Check minimum amount
        if self.minimum_amount and order_amount < self.minimum_amount:
            return False, f"Minimum order amount of ${self.minimum_amount} required"
        
        # Check per-user usage limit
        if user_id and self.usage_limit_per_user:
            from app.models.order import Order
            user_usage = Order.query.filter_by(
                user_id=user_id, 
                coupon_id=self.id
            ).filter(Order.status.in_(['confirmed', 'processing', 'shipped', 'delivered'])).count()
            
            if user_usage >= self.usage_limit_per_user:
                return False, "You have already used this coupon the maximum number of times"
        
        return True, "Coupon is valid"
    
    def calculate_discount(self, order_amount):
        """Calculate discount amount for given order amount"""
        if self.discount_type == 'percentage':
            discount = (order_amount * self.discount_value) / 100
            # Apply maximum discount limit if set
            if self.maximum_discount:
                discount = min(discount, self.maximum_discount)
        else:  # fixed amount
            discount = min(self.discount_value, order_amount)
        
        return discount
    
    def apply_coupon(self, user_id=None, order_amount=0):
        """Apply coupon and return discount amount"""
        is_valid, message = self.is_valid(user_id, order_amount)
        
        if not is_valid:
            return 0, message
        
        discount = self.calculate_discount(order_amount)
        return discount, "Coupon applied successfully"
    
    def increment_usage(self):
        """Increment usage count"""
        self.used_count += 1
        self.updated_at = datetime.utcnow()
    
    def get_usage_percentage(self):
        """Get usage percentage"""
        if not self.usage_limit:
            return 0
        return (self.used_count / self.usage_limit) * 100
    
    @property
    def is_expired(self):
        """Check if coupon is expired"""
        return date.today() > self.valid_until
    
    @property
    def days_until_expiry(self):
        """Get days until expiry"""
        if self.is_expired:
            return 0
        return (self.valid_until - date.today()).days
    
    def to_dict(self):
        """Convert coupon to dictionary"""
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'description': self.description,
            'discount_type': self.discount_type,
            'discount_value': float(self.discount_value),
            'minimum_amount': float(self.minimum_amount) if self.minimum_amount else None,
            'valid_from': self.valid_from.isoformat(),
            'valid_until': self.valid_until.isoformat(),
            'is_active': self.is_active,
            'used_count': self.used_count,
            'usage_limit': self.usage_limit
        }
    
    def __repr__(self):
        return f'<Coupon {self.code}>'
