from datetime import datetime
from app.extensions import db

class Wishlist(db.Model):
    __tablename__ = 'wishlist'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Foreign keys
    user_id = db.Column(db.In<PERSON>ger, db.<PERSON>('users.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    
    # Unique constraint to prevent duplicate wishlist items
    __table_args__ = (db.UniqueConstraint('user_id', 'product_id', name='unique_user_product_wishlist'),)
    
    def to_dict(self):
        """Convert wishlist item to dictionary"""
        return {
            'id': self.id,
            'product_id': self.product_id,
            'product_name': self.product.name,
            'product_price': float(self.product.price),
            'product_image': self.product.main_image,
            'product_slug': self.product.slug,
            'is_in_stock': self.product.is_in_stock,
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        return f'<Wishlist {self.user.username} - {self.product.name}>'
