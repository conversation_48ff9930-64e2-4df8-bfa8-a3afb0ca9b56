from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required
from app import db
from app.models.product import Product, Category, ProductImage
from app.models.order import Order, OrderItem
from app.models.coupon import Coupon
from app.models.user import User
from app.models.review import Review
from app.forms.admin import CategoryForm, ProductForm, ProductImageForm, CouponForm, OrderStatusForm
from app.utils.decorators import admin_required
from app.utils.helpers import save_picture, create_slug
from sqlalchemy import func, desc
from datetime import datetime, timedelta

bp = Blueprint('admin', __name__)

@bp.route('/')
@admin_required
def dashboard():
    # Get statistics
    total_products = Product.query.filter_by(is_active=True).count()
    total_orders = Order.query.count()
    total_users = User.query.filter_by(is_active=True).count()
    total_revenue = db.session.query(func.sum(Order.total_amount)).filter(
        Order.status.in_(['completed', 'delivered'])
    ).scalar() or 0
    
    # Recent orders
    recent_orders = Order.query.order_by(Order.created_at.desc()).limit(5).all()
    
    # Low stock products
    low_stock_products = Product.query.filter(
        Product.is_active == True,
        Product.track_inventory == True,
        Product.stock_quantity <= Product.low_stock_threshold
    ).limit(5).all()
    
    # Pending reviews
    pending_reviews = Review.query.filter_by(is_approved=False).count()
    
    # Sales data for chart (last 7 days)
    sales_data = []
    for i in range(7):
        date = datetime.utcnow().date() - timedelta(days=i)
        daily_sales = db.session.query(func.sum(Order.total_amount)).filter(
            func.date(Order.created_at) == date,
            Order.status.in_(['completed', 'delivered'])
        ).scalar() or 0
        sales_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'sales': float(daily_sales)
        })
    
    return render_template('admin/dashboard.html',
                         title='Admin Dashboard',
                         total_products=total_products,
                         total_orders=total_orders,
                         total_users=total_users,
                         total_revenue=total_revenue,
                         recent_orders=recent_orders,
                         low_stock_products=low_stock_products,
                         pending_reviews=pending_reviews,
                         sales_data=sales_data)

# Category Management
@bp.route('/categories')
@admin_required
def categories():
    page = request.args.get('page', 1, type=int)
    categories = Category.query.order_by(Category.sort_order, Category.name).paginate(
        page=page, per_page=20, error_out=False
    )
    return render_template('admin/categories.html', title='Categories', categories=categories)

@bp.route('/categories/add', methods=['GET', 'POST'])
@admin_required
def add_category():
    form = CategoryForm()
    
    if form.validate_on_submit():
        category = Category(
            name=form.name.data,
            slug=create_slug(form.name.data),
            description=form.description.data,
            is_active=form.is_active.data,
            sort_order=form.sort_order.data,
            meta_title=form.meta_title.data,
            meta_description=form.meta_description.data,
            meta_keywords=form.meta_keywords.data
        )
        
        if form.image.data:
            image_filename = save_picture(form.image.data, 'categories', (300, 300))
            category.image = image_filename
        
        db.session.add(category)
        db.session.commit()
        
        flash('Category created successfully!', 'success')
        return redirect(url_for('admin.categories'))
    
    return render_template('admin/category_form.html', title='Add Category', form=form)

@bp.route('/categories/<int:id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_category(id):
    category = Category.query.get_or_404(id)
    form = CategoryForm(obj=category)
    
    if form.validate_on_submit():
        category.name = form.name.data
        category.slug = create_slug(form.name.data)
        category.description = form.description.data
        category.is_active = form.is_active.data
        category.sort_order = form.sort_order.data
        category.meta_title = form.meta_title.data
        category.meta_description = form.meta_description.data
        category.meta_keywords = form.meta_keywords.data
        
        if form.image.data:
            image_filename = save_picture(form.image.data, 'categories', (300, 300))
            category.image = image_filename
        
        db.session.commit()
        
        flash('Category updated successfully!', 'success')
        return redirect(url_for('admin.categories'))
    
    return render_template('admin/category_form.html', title='Edit Category', form=form, category=category)

@bp.route('/categories/<int:id>/delete', methods=['POST'])
@admin_required
def delete_category(id):
    category = Category.query.get_or_404(id)
    
    # Check if category has products
    if category.products.count() > 0:
        flash('Cannot delete category with products. Please move or delete products first.', 'error')
    else:
        db.session.delete(category)
        db.session.commit()
        flash('Category deleted successfully!', 'success')
    
    return redirect(url_for('admin.categories'))

# Product Management
@bp.route('/products')
@admin_required
def products():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    category_id = request.args.get('category', type=int)
    
    query = Product.query
    
    if search:
        query = query.filter(Product.name.contains(search) | Product.sku.contains(search))
    
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    products = query.order_by(Product.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    categories = Category.query.filter_by(is_active=True).order_by(Category.name).all()
    
    return render_template('admin/products.html', 
                         title='Products', 
                         products=products, 
                         categories=categories,
                         search=search,
                         selected_category=category_id)

@bp.route('/products/add', methods=['GET', 'POST'])
@admin_required
def add_product():
    form = ProductForm()
    
    if form.validate_on_submit():
        product = Product(
            name=form.name.data,
            slug=create_slug(form.name.data),
            description=form.description.data,
            short_description=form.short_description.data,
            price=form.price.data,
            compare_price=form.compare_price.data,
            cost_price=form.cost_price.data,
            sku=form.sku.data,
            stock_quantity=form.stock_quantity.data,
            low_stock_threshold=form.low_stock_threshold.data,
            track_inventory=form.track_inventory.data,
            weight=form.weight.data,
            dimensions=form.dimensions.data,
            brand=form.brand.data,
            model=form.model.data,
            color=form.color.data,
            size=form.size.data,
            material=form.material.data,
            category_id=form.category_id.data,
            is_active=form.is_active.data,
            is_featured=form.is_featured.data,
            is_digital=form.is_digital.data,
            meta_title=form.meta_title.data,
            meta_description=form.meta_description.data,
            meta_keywords=form.meta_keywords.data
        )
        
        db.session.add(product)
        db.session.commit()
        
        flash('Product created successfully!', 'success')
        return redirect(url_for('admin.edit_product', id=product.id))
    
    return render_template('admin/product_form.html', title='Add Product', form=form)

@bp.route('/products/<int:id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_product(id):
    product = Product.query.get_or_404(id)
    form = ProductForm(obj=product)
    form.product = product  # For SKU validation
    
    if form.validate_on_submit():
        product.name = form.name.data
        product.slug = create_slug(form.name.data)
        product.description = form.description.data
        product.short_description = form.short_description.data
        product.price = form.price.data
        product.compare_price = form.compare_price.data
        product.cost_price = form.cost_price.data
        product.sku = form.sku.data
        product.stock_quantity = form.stock_quantity.data
        product.low_stock_threshold = form.low_stock_threshold.data
        product.track_inventory = form.track_inventory.data
        product.weight = form.weight.data
        product.dimensions = form.dimensions.data
        product.brand = form.brand.data
        product.model = form.model.data
        product.color = form.color.data
        product.size = form.size.data
        product.material = form.material.data
        product.category_id = form.category_id.data
        product.is_active = form.is_active.data
        product.is_featured = form.is_featured.data
        product.is_digital = form.is_digital.data
        product.meta_title = form.meta_title.data
        product.meta_description = form.meta_description.data
        product.meta_keywords = form.meta_keywords.data
        
        db.session.commit()
        
        flash('Product updated successfully!', 'success')
        return redirect(url_for('admin.products'))
    
    # Get product images
    images = product.images.order_by(ProductImage.sort_order, ProductImage.created_at).all()
    image_form = ProductImageForm()
    
    return render_template('admin/product_form.html',
                         title='Edit Product',
                         form=form,
                         product=product,
                         images=images,
                         image_form=image_form)

@bp.route('/products/<int:id>/images/add', methods=['POST'])
@admin_required
def add_product_image(id):
    product = Product.query.get_or_404(id)
    form = ProductImageForm()

    if form.validate_on_submit():
        # If this is set as main image, unset other main images
        if form.is_main.data:
            ProductImage.query.filter_by(product_id=product.id, is_main=True).update({'is_main': False})

        image_filename = save_picture(form.image.data, 'products', (800, 800))

        image = ProductImage(
            product_id=product.id,
            filename=image_filename,
            alt_text=form.alt_text.data,
            is_main=form.is_main.data,
            sort_order=form.sort_order.data
        )

        db.session.add(image)
        db.session.commit()

        flash('Image uploaded successfully!', 'success')

    return redirect(url_for('admin.edit_product', id=id))

@bp.route('/products/<int:product_id>/images/<int:image_id>/delete', methods=['POST'])
@admin_required
def delete_product_image(product_id, image_id):
    image = ProductImage.query.filter_by(id=image_id, product_id=product_id).first_or_404()

    db.session.delete(image)
    db.session.commit()

    flash('Image deleted successfully!', 'success')
    return redirect(url_for('admin.edit_product', id=product_id))

@bp.route('/products/<int:id>/delete', methods=['POST'])
@admin_required
def delete_product(id):
    product = Product.query.get_or_404(id)

    # Check if product has orders
    if product.order_items.count() > 0:
        # Don't delete, just deactivate
        product.is_active = False
        db.session.commit()
        flash('Product deactivated (has order history).', 'info')
    else:
        db.session.delete(product)
        db.session.commit()
        flash('Product deleted successfully!', 'success')

    return redirect(url_for('admin.products'))

# Order Management
@bp.route('/orders')
@admin_required
def orders():
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', '')
    search = request.args.get('search', '')

    query = Order.query

    if status:
        query = query.filter_by(status=status)

    if search:
        query = query.filter(
            Order.order_number.contains(search) |
            Order.customer_email.contains(search)
        )

    orders = query.order_by(Order.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('admin/orders.html',
                         title='Orders',
                         orders=orders,
                         selected_status=status,
                         search=search)

@bp.route('/orders/<int:id>')
@admin_required
def order_detail(id):
    order = Order.query.get_or_404(id)
    form = OrderStatusForm(obj=order)

    return render_template('admin/order_detail.html',
                         title=f'Order {order.order_number}',
                         order=order,
                         form=form)

@bp.route('/orders/<int:id>/update', methods=['POST'])
@admin_required
def update_order(id):
    order = Order.query.get_or_404(id)
    form = OrderStatusForm()

    if form.validate_on_submit():
        order.update_status(form.status.data)
        order.tracking_number = form.tracking_number.data
        order.admin_notes = form.admin_notes.data

        db.session.commit()

        flash('Order updated successfully!', 'success')

    return redirect(url_for('admin.order_detail', id=id))

# Coupon Management
@bp.route('/coupons')
@admin_required
def coupons():
    page = request.args.get('page', 1, type=int)
    coupons = Coupon.query.order_by(Coupon.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    return render_template('admin/coupons.html', title='Coupons', coupons=coupons)

@bp.route('/coupons/add', methods=['GET', 'POST'])
@admin_required
def add_coupon():
    form = CouponForm()

    if form.validate_on_submit():
        coupon = Coupon(
            code=form.code.data.upper(),
            name=form.name.data,
            description=form.description.data,
            discount_type=form.discount_type.data,
            discount_value=form.discount_value.data,
            usage_limit=form.usage_limit.data,
            usage_limit_per_user=form.usage_limit_per_user.data,
            minimum_amount=form.minimum_amount.data,
            maximum_discount=form.maximum_discount.data,
            valid_from=form.valid_from.data,
            valid_until=form.valid_until.data,
            is_active=form.is_active.data
        )

        db.session.add(coupon)
        db.session.commit()

        flash('Coupon created successfully!', 'success')
        return redirect(url_for('admin.coupons'))

    return render_template('admin/coupon_form.html', title='Add Coupon', form=form)

@bp.route('/coupons/<int:id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_coupon(id):
    coupon = Coupon.query.get_or_404(id)
    form = CouponForm(obj=coupon)
    form.coupon = coupon  # For code validation

    if form.validate_on_submit():
        coupon.code = form.code.data.upper()
        coupon.name = form.name.data
        coupon.description = form.description.data
        coupon.discount_type = form.discount_type.data
        coupon.discount_value = form.discount_value.data
        coupon.usage_limit = form.usage_limit.data
        coupon.usage_limit_per_user = form.usage_limit_per_user.data
        coupon.minimum_amount = form.minimum_amount.data
        coupon.maximum_discount = form.maximum_discount.data
        coupon.valid_from = form.valid_from.data
        coupon.valid_until = form.valid_until.data
        coupon.is_active = form.is_active.data

        db.session.commit()

        flash('Coupon updated successfully!', 'success')
        return redirect(url_for('admin.coupons'))

    return render_template('admin/coupon_form.html', title='Edit Coupon', form=form, coupon=coupon)

@bp.route('/coupons/<int:id>/delete', methods=['POST'])
@admin_required
def delete_coupon(id):
    coupon = Coupon.query.get_or_404(id)

    # Check if coupon has been used
    if coupon.used_count > 0:
        # Don't delete, just deactivate
        coupon.is_active = False
        db.session.commit()
        flash('Coupon deactivated (has usage history).', 'info')
    else:
        db.session.delete(coupon)
        db.session.commit()
        flash('Coupon deleted successfully!', 'success')

    return redirect(url_for('admin.coupons'))

# User Management
@bp.route('/users')
@admin_required
def users():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')

    query = User.query

    if search:
        query = query.filter(
            User.username.contains(search) |
            User.email.contains(search) |
            User.first_name.contains(search) |
            User.last_name.contains(search)
        )

    users = query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('admin/users.html',
                         title='Users',
                         users=users,
                         search=search)

@bp.route('/users/<int:id>')
@admin_required
def user_detail(id):
    user = User.query.get_or_404(id)
    recent_orders = user.orders.order_by(Order.created_at.desc()).limit(10).all()

    return render_template('admin/user_detail.html',
                         title=f'User: {user.username}',
                         user=user,
                         recent_orders=recent_orders)

# Review Management
@bp.route('/reviews')
@admin_required
def reviews():
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', 'pending')

    query = Review.query

    if status == 'pending':
        query = query.filter_by(is_approved=False)
    elif status == 'approved':
        query = query.filter_by(is_approved=True)

    reviews = query.order_by(Review.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('admin/reviews.html',
                         title='Reviews',
                         reviews=reviews,
                         selected_status=status)

@bp.route('/reviews/<int:id>/approve', methods=['POST'])
@admin_required
def approve_review(id):
    review = Review.query.get_or_404(id)
    review.is_approved = True
    db.session.commit()

    flash('Review approved!', 'success')
    return redirect(url_for('admin.reviews'))

@bp.route('/reviews/<int:id>/reject', methods=['POST'])
@admin_required
def reject_review(id):
    review = Review.query.get_or_404(id)
    db.session.delete(review)
    db.session.commit()

    flash('Review rejected and deleted!', 'info')
    return redirect(url_for('admin.reviews'))
