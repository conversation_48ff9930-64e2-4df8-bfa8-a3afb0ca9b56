from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import current_user, login_required
from app import db
from app.models.product import Product, Category
from app.models.review import Review
from app.models.wishlist import Wishlist
from app.forms.shop import ReviewForm
from app.utils.helpers import get_breadcrumbs, paginate_query
from sqlalchemy import or_, and_

bp = Blueprint('shop', __name__)

@bp.route('/products')
def products():
    page = request.args.get('page', 1, type=int)
    category_slug = request.args.get('category')
    sort_by = request.args.get('sort', 'newest')
    min_price = request.args.get('min_price', type=float)
    max_price = request.args.get('max_price', type=float)
    
    # Base query
    query = Product.query.filter_by(is_active=True)
    
    # Filter by category
    category = None
    if category_slug:
        category = Category.query.filter_by(slug=category_slug, is_active=True).first_or_404()
        query = query.filter_by(category_id=category.id)
    
    # Filter by price range
    if min_price is not None:
        query = query.filter(Product.price >= min_price)
    if max_price is not None:
        query = query.filter(Product.price <= max_price)
    
    # Sort products
    if sort_by == 'price_low':
        query = query.order_by(Product.price.asc())
    elif sort_by == 'price_high':
        query = query.order_by(Product.price.desc())
    elif sort_by == 'name':
        query = query.order_by(Product.name.asc())
    elif sort_by == 'featured':
        query = query.order_by(Product.is_featured.desc(), Product.created_at.desc())
    else:  # newest
        query = query.order_by(Product.created_at.desc())
    
    # Paginate results
    products = query.paginate(
        page=page, per_page=12, error_out=False
    )
    
    # Get all categories for filter
    categories = Category.query.filter_by(is_active=True).order_by(Category.name).all()
    
    # Get price range for filter
    price_range = db.session.query(
        db.func.min(Product.price),
        db.func.max(Product.price)
    ).filter_by(is_active=True).first()
    
    breadcrumbs = get_breadcrumbs(category=category)
    
    return render_template('shop/products.html',
                         title='Shop' + (f' - {category.name}' if category else ''),
                         products=products,
                         categories=categories,
                         current_category=category,
                         price_range=price_range,
                         breadcrumbs=breadcrumbs,
                         sort_by=sort_by)

@bp.route('/category/<slug>')
def category(slug):
    category = Category.query.filter_by(slug=slug, is_active=True).first_or_404()
    return redirect(url_for('shop.products', category=slug))

@bp.route('/product/<slug>')
def product_detail(slug):
    product = Product.query.filter_by(slug=slug, is_active=True).first_or_404()
    
    # Get related products from same category
    related_products = Product.query.filter(
        and_(
            Product.category_id == product.category_id,
            Product.id != product.id,
            Product.is_active == True
        )
    ).limit(4).all()
    
    # Get product reviews
    reviews = Review.query.filter_by(
        product_id=product.id, 
        is_approved=True
    ).order_by(Review.created_at.desc()).limit(10).all()
    
    # Check if user has this product in wishlist
    in_wishlist = False
    if current_user.is_authenticated:
        in_wishlist = Wishlist.query.filter_by(
            user_id=current_user.id,
            product_id=product.id
        ).first() is not None
    
    breadcrumbs = get_breadcrumbs(category=product.category, product=product)
    
    return render_template('shop/product_detail.html',
                         title=product.name,
                         product=product,
                         related_products=related_products,
                         reviews=reviews,
                         in_wishlist=in_wishlist,
                         breadcrumbs=breadcrumbs)

@bp.route('/product/<slug>/review', methods=['POST'])
@login_required
def add_review(slug):
    product = Product.query.filter_by(slug=slug, is_active=True).first_or_404()
    form = ReviewForm()
    
    if form.validate_on_submit():
        # Check if user already reviewed this product
        existing_review = Review.query.filter_by(
            user_id=current_user.id,
            product_id=product.id
        ).first()
        
        if existing_review:
            flash('You have already reviewed this product.', 'warning')
        else:
            review = Review(
                user_id=current_user.id,
                product_id=product.id,
                rating=form.rating.data,
                title=form.title.data,
                comment=form.comment.data
            )
            
            # Check if user has purchased this product
            from app.models.order import Order, OrderItem
            has_purchased = db.session.query(OrderItem).join(Order).filter(
                Order.user_id == current_user.id,
                OrderItem.product_id == product.id,
                Order.status.in_(['completed', 'delivered'])
            ).first() is not None
            
            review.is_verified_purchase = has_purchased
            
            db.session.add(review)
            db.session.commit()
            
            flash('Your review has been submitted and is pending approval.', 'success')
    
    return redirect(url_for('shop.product_detail', slug=slug))

@bp.route('/wishlist/toggle/<int:product_id>', methods=['POST'])
@login_required
def toggle_wishlist(product_id):
    product = Product.query.get_or_404(product_id)
    
    wishlist_item = Wishlist.query.filter_by(
        user_id=current_user.id,
        product_id=product_id
    ).first()
    
    if wishlist_item:
        db.session.delete(wishlist_item)
        action = 'removed'
    else:
        wishlist_item = Wishlist(
            user_id=current_user.id,
            product_id=product_id
        )
        db.session.add(wishlist_item)
        action = 'added'
    
    db.session.commit()
    
    return jsonify({
        'success': True,
        'action': action,
        'message': f'Product {action} {"to" if action == "added" else "from"} wishlist'
    })

@bp.route('/api/products/search')
def api_search_products():
    """API endpoint for product search autocomplete"""
    query = request.args.get('q', '')
    
    if len(query) < 2:
        return jsonify([])
    
    products = Product.query.filter(
        Product.is_active == True,
        Product.name.contains(query)
    ).limit(10).all()
    
    results = []
    for product in products:
        results.append({
            'id': product.id,
            'name': product.name,
            'price': float(product.price),
            'image': product.main_image,
            'url': url_for('shop.product_detail', slug=product.slug)
        })
    
    return jsonify(results)
