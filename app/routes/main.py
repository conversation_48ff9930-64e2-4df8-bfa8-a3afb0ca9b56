from flask import Blueprint, render_template, request, jsonify
from flask_login import current_user
from app.models.product import Product, Category
from app.models.order import Order
from app.utils.decorators import admin_required
from app.utils.helpers import get_cart_count
from sqlalchemy import func

bp = Blueprint('main', __name__)

@bp.route('/')
def index():
    # Get featured products
    featured_products = Product.query.filter_by(is_featured=True, is_active=True).limit(8).all()
    
    # Get latest products
    latest_products = Product.query.filter_by(is_active=True).order_by(Product.created_at.desc()).limit(8).all()
    
    # Get categories
    categories = Category.query.filter_by(is_active=True).order_by(Category.sort_order).limit(6).all()
    
    return render_template('main/index.html', 
                         featured_products=featured_products,
                         latest_products=latest_products,
                         categories=categories)

@bp.route('/about')
def about():
    return render_template('main/about.html', title='About Us')

@bp.route('/contact')
def contact():
    return render_template('main/contact.html', title='Contact Us')

@bp.route('/search')
def search():
    query = request.args.get('q', '')
    page = request.args.get('page', 1, type=int)
    
    if query:
        products = Product.query.filter(
            Product.is_active == True,
            Product.name.contains(query) | 
            Product.description.contains(query) |
            Product.short_description.contains(query)
        ).paginate(
            page=page, per_page=12, error_out=False
        )
    else:
        products = Product.query.filter_by(is_active=True).paginate(
            page=page, per_page=12, error_out=False
        )
    
    return render_template('main/search.html', 
                         title=f'Search Results for "{query}"',
                         products=products, 
                         query=query)

@bp.route('/dashboard')
@admin_required
def dashboard():
    # Get statistics for admin dashboard
    total_products = Product.query.filter_by(is_active=True).count()
    total_orders = Order.query.count()
    total_revenue = db.session.query(func.sum(Order.total_amount)).filter(
        Order.status.in_(['completed', 'delivered'])
    ).scalar() or 0
    
    # Recent orders
    recent_orders = Order.query.order_by(Order.created_at.desc()).limit(5).all()
    
    # Low stock products
    low_stock_products = Product.query.filter(
        Product.is_active == True,
        Product.track_inventory == True,
        Product.stock_quantity <= Product.low_stock_threshold
    ).limit(5).all()
    
    return render_template('main/dashboard.html',
                         title='Admin Dashboard',
                         total_products=total_products,
                         total_orders=total_orders,
                         total_revenue=total_revenue,
                         recent_orders=recent_orders,
                         low_stock_products=low_stock_products)

@bp.context_processor
def inject_cart_count():
    """Inject cart count into all templates"""
    cart_count = 0
    if current_user.is_authenticated:
        cart_count = get_cart_count(current_user)
    return dict(cart_count=cart_count)

@bp.context_processor
def inject_categories():
    """Inject categories into all templates for navigation"""
    categories = Category.query.filter_by(is_active=True).order_by(Category.sort_order).all()
    return dict(nav_categories=categories)
