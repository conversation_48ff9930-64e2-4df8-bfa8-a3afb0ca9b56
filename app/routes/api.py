from flask import Blueprint, request, jsonify
from flask_login import current_user, login_required
from app.extensions import db
from app.models.product import Product, Category
from app.models.cart import Cart, CartItem
from app.models.wishlist import Wishlist
from app.models.review import Review
from app.models.order import Order
from app.utils.decorators import admin_required
from sqlalchemy import func

bp = Blueprint('api', __name__)

# Product API
@bp.route('/products')
def api_products():
    """Get products with filtering and pagination"""
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 12, type=int), 100)
    category_id = request.args.get('category_id', type=int)
    search = request.args.get('search', '')
    sort_by = request.args.get('sort', 'newest')
    min_price = request.args.get('min_price', type=float)
    max_price = request.args.get('max_price', type=float)
    
    query = Product.query.filter_by(is_active=True)
    
    # Apply filters
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    if search:
        query = query.filter(
            Product.name.contains(search) |
            Product.description.contains(search)
        )
    
    if min_price is not None:
        query = query.filter(Product.price >= min_price)
    
    if max_price is not None:
        query = query.filter(Product.price <= max_price)
    
    # Apply sorting
    if sort_by == 'price_low':
        query = query.order_by(Product.price.asc())
    elif sort_by == 'price_high':
        query = query.order_by(Product.price.desc())
    elif sort_by == 'name':
        query = query.order_by(Product.name.asc())
    elif sort_by == 'featured':
        query = query.order_by(Product.is_featured.desc(), Product.created_at.desc())
    else:  # newest
        query = query.order_by(Product.created_at.desc())
    
    # Paginate
    products = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'products': [product.to_dict() for product in products.items],
        'pagination': {
            'page': products.page,
            'pages': products.pages,
            'per_page': products.per_page,
            'total': products.total,
            'has_next': products.has_next,
            'has_prev': products.has_prev
        }
    })

@bp.route('/products/<int:product_id>')
def api_product_detail(product_id):
    """Get single product details"""
    product = Product.query.filter_by(id=product_id, is_active=True).first_or_404()
    
    # Get product images
    images = [{'filename': img.filename, 'alt_text': img.alt_text} 
              for img in product.images.order_by('sort_order').all()]
    
    # Get reviews
    reviews = [review.to_dict() for review in 
               product.reviews.filter_by(is_approved=True).order_by(Review.created_at.desc()).limit(5).all()]
    
    product_data = product.to_dict()
    product_data.update({
        'images': images,
        'reviews': reviews,
        'description': product.description,
        'short_description': product.short_description
    })
    
    return jsonify(product_data)

# Categories API
@bp.route('/categories')
def api_categories():
    """Get all active categories"""
    categories = Category.query.filter_by(is_active=True).order_by(Category.sort_order, Category.name).all()
    
    return jsonify([{
        'id': cat.id,
        'name': cat.name,
        'slug': cat.slug,
        'description': cat.description,
        'image': cat.image,
        'product_count': cat.get_product_count()
    } for cat in categories])

# Cart API
@bp.route('/cart')
@login_required
def api_cart():
    """Get user's cart"""
    if not current_user.cart:
        return jsonify({'items': [], 'total_items': 0, 'subtotal': 0})
    
    return jsonify(current_user.cart.to_dict())

@bp.route('/cart/add', methods=['POST'])
@login_required
def api_cart_add():
    """Add item to cart"""
    data = request.get_json()
    product_id = data.get('product_id')
    quantity = data.get('quantity', 1)
    
    if not product_id:
        return jsonify({'error': 'Product ID required'}), 400
    
    product = Product.query.filter_by(id=product_id, is_active=True).first()
    if not product:
        return jsonify({'error': 'Product not found'}), 404
    
    if product.track_inventory and quantity > product.stock_quantity:
        return jsonify({'error': f'Only {product.stock_quantity} items available'}), 400
    
    if not current_user.cart:
        current_user.cart = Cart()
        db.session.add(current_user.cart)
    
    current_user.cart.add_item(product, quantity)
    
    return jsonify({
        'message': 'Item added to cart',
        'cart': current_user.cart.to_dict()
    })

@bp.route('/cart/update', methods=['PUT'])
@login_required
def api_cart_update():
    """Update cart item quantity"""
    data = request.get_json()
    product_id = data.get('product_id')
    quantity = data.get('quantity', 0)
    
    if not current_user.cart:
        return jsonify({'error': 'Cart not found'}), 404
    
    if quantity <= 0:
        current_user.cart.remove_item(product_id)
    else:
        current_user.cart.update_item_quantity(product_id, quantity)
    
    return jsonify({
        'message': 'Cart updated',
        'cart': current_user.cart.to_dict()
    })

@bp.route('/cart/remove', methods=['DELETE'])
@login_required
def api_cart_remove():
    """Remove item from cart"""
    data = request.get_json()
    product_id = data.get('product_id')
    
    if not current_user.cart:
        return jsonify({'error': 'Cart not found'}), 404
    
    current_user.cart.remove_item(product_id)
    
    return jsonify({
        'message': 'Item removed from cart',
        'cart': current_user.cart.to_dict()
    })

# Wishlist API
@bp.route('/wishlist')
@login_required
def api_wishlist():
    """Get user's wishlist"""
    items = current_user.wishlist_items.all()
    return jsonify([item.to_dict() for item in items])

@bp.route('/wishlist/toggle', methods=['POST'])
@login_required
def api_wishlist_toggle():
    """Toggle product in wishlist"""
    data = request.get_json()
    product_id = data.get('product_id')
    
    if not product_id:
        return jsonify({'error': 'Product ID required'}), 400
    
    product = Product.query.filter_by(id=product_id, is_active=True).first()
    if not product:
        return jsonify({'error': 'Product not found'}), 404
    
    wishlist_item = Wishlist.query.filter_by(
        user_id=current_user.id,
        product_id=product_id
    ).first()
    
    if wishlist_item:
        db.session.delete(wishlist_item)
        action = 'removed'
    else:
        wishlist_item = Wishlist(
            user_id=current_user.id,
            product_id=product_id
        )
        db.session.add(wishlist_item)
        action = 'added'
    
    db.session.commit()
    
    return jsonify({
        'action': action,
        'message': f'Product {action} {"to" if action == "added" else "from"} wishlist'
    })

# Reviews API
@bp.route('/products/<int:product_id>/reviews')
def api_product_reviews(product_id):
    """Get product reviews"""
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 10, type=int), 50)
    
    product = Product.query.filter_by(id=product_id, is_active=True).first_or_404()
    
    reviews = Review.query.filter_by(
        product_id=product_id,
        is_approved=True
    ).order_by(Review.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'reviews': [review.to_dict() for review in reviews.items],
        'pagination': {
            'page': reviews.page,
            'pages': reviews.pages,
            'per_page': reviews.per_page,
            'total': reviews.total
        },
        'average_rating': product.get_average_rating(),
        'total_reviews': product.get_review_count()
    })

# Search API
@bp.route('/search')
def api_search():
    """Search products"""
    query = request.args.get('q', '')
    limit = min(request.args.get('limit', 10, type=int), 50)
    
    if len(query) < 2:
        return jsonify([])
    
    products = Product.query.filter(
        Product.is_active == True,
        Product.name.contains(query) |
        Product.description.contains(query)
    ).limit(limit).all()
    
    return jsonify([{
        'id': product.id,
        'name': product.name,
        'slug': product.slug,
        'price': float(product.price),
        'image': product.main_image,
        'category': product.category.name
    } for product in products])

# Admin API
@bp.route('/admin/stats')
@admin_required
def api_admin_stats():
    """Get admin dashboard statistics"""
    from datetime import datetime, timedelta
    
    # Basic stats
    total_products = Product.query.filter_by(is_active=True).count()
    total_orders = Order.query.count()
    total_revenue = db.session.query(func.sum(Order.total_amount)).filter(
        Order.status.in_(['completed', 'delivered'])
    ).scalar() or 0
    
    # Recent stats (last 30 days)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    recent_orders = Order.query.filter(Order.created_at >= thirty_days_ago).count()
    recent_revenue = db.session.query(func.sum(Order.total_amount)).filter(
        Order.created_at >= thirty_days_ago,
        Order.status.in_(['completed', 'delivered'])
    ).scalar() or 0
    
    # Low stock products
    low_stock_count = Product.query.filter(
        Product.is_active == True,
        Product.track_inventory == True,
        Product.stock_quantity <= Product.low_stock_threshold
    ).count()
    
    # Pending reviews
    pending_reviews = Review.query.filter_by(is_approved=False).count()
    
    return jsonify({
        'total_products': total_products,
        'total_orders': total_orders,
        'total_revenue': float(total_revenue),
        'recent_orders': recent_orders,
        'recent_revenue': float(recent_revenue),
        'low_stock_count': low_stock_count,
        'pending_reviews': pending_reviews
    })
