from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import current_user, login_required
from app.extensions import db
from app.models.product import Product
from app.models.cart import Cart, CartItem
from app.models.coupon import Coupon
from app.forms.cart import CheckoutForm, CouponForm
from app.utils.helpers import calculate_tax, calculate_shipping

bp = Blueprint('cart', __name__)

@bp.route('/')
def view_cart():
    cart_items = []
    subtotal = 0
    
    if current_user.is_authenticated and current_user.cart:
        cart_items = current_user.cart.items.all()
        subtotal = current_user.cart.get_subtotal()
    
    # Calculate totals
    tax_amount = calculate_tax(subtotal)
    shipping_amount = calculate_shipping(0)  # Will be calculated based on items
    total = subtotal + tax_amount + shipping_amount
    
    return render_template('cart/view.html',
                         title='Shopping Cart',
                         cart_items=cart_items,
                         subtotal=subtotal,
                         tax_amount=tax_amount,
                         shipping_amount=shipping_amount,
                         total=total)

@bp.route('/add/<int:product_id>', methods=['POST'])
def add_to_cart(product_id):
    product = Product.query.get_or_404(product_id)
    quantity = request.form.get('quantity', 1, type=int)
    
    if not product.is_active:
        flash('This product is no longer available.', 'error')
        return redirect(url_for('shop.product_detail', slug=product.slug))
    
    if product.track_inventory and quantity > product.stock_quantity:
        flash(f'Only {product.stock_quantity} items available in stock.', 'error')
        return redirect(url_for('shop.product_detail', slug=product.slug))
    
    if current_user.is_authenticated:
        # Add to user's cart
        if not current_user.cart:
            current_user.cart = Cart()
            db.session.add(current_user.cart)
        
        current_user.cart.add_item(product, quantity)
        flash(f'{product.name} added to cart!', 'success')
    else:
        # Handle guest cart using session
        cart = session.get('cart', {})
        product_id_str = str(product_id)
        
        if product_id_str in cart:
            cart[product_id_str] += quantity
        else:
            cart[product_id_str] = quantity
        
        session['cart'] = cart
        flash(f'{product.name} added to cart!', 'success')
    
    return redirect(url_for('shop.product_detail', slug=product.slug))

@bp.route('/update/<int:product_id>', methods=['POST'])
def update_cart_item(product_id):
    quantity = request.form.get('quantity', 0, type=int)
    
    if current_user.is_authenticated and current_user.cart:
        if quantity <= 0:
            current_user.cart.remove_item(product_id)
            flash('Item removed from cart.', 'info')
        else:
            current_user.cart.update_item_quantity(product_id, quantity)
            flash('Cart updated.', 'success')
    else:
        # Handle guest cart
        cart = session.get('cart', {})
        product_id_str = str(product_id)
        
        if quantity <= 0:
            cart.pop(product_id_str, None)
            flash('Item removed from cart.', 'info')
        else:
            cart[product_id_str] = quantity
            flash('Cart updated.', 'success')
        
        session['cart'] = cart
    
    return redirect(url_for('cart.view_cart'))

@bp.route('/remove/<int:product_id>', methods=['POST'])
def remove_from_cart(product_id):
    if current_user.is_authenticated and current_user.cart:
        current_user.cart.remove_item(product_id)
    else:
        # Handle guest cart
        cart = session.get('cart', {})
        cart.pop(str(product_id), None)
        session['cart'] = cart
    
    flash('Item removed from cart.', 'info')
    return redirect(url_for('cart.view_cart'))

@bp.route('/clear', methods=['POST'])
def clear_cart():
    if current_user.is_authenticated and current_user.cart:
        current_user.cart.clear()
    else:
        session.pop('cart', None)
    
    flash('Cart cleared.', 'info')
    return redirect(url_for('cart.view_cart'))

@bp.route('/checkout', methods=['GET', 'POST'])
@login_required
def checkout():
    if not current_user.cart or current_user.cart.get_total_items() == 0:
        flash('Your cart is empty.', 'warning')
        return redirect(url_for('shop.products'))

    form = CheckoutForm()
    coupon_form = CouponForm()

    if form.validate_on_submit():
        # Process the order
        from app.models.order import Order, OrderItem

        # Calculate totals
        subtotal = current_user.cart.get_subtotal()
        tax_amount = calculate_tax(subtotal)
        shipping_amount = calculate_shipping(current_user.cart.get_total_weight(), form.shipping_method.data)

        # Apply coupon if in session
        discount_amount = 0
        coupon = None
        if 'applied_coupon' in session:
            coupon = Coupon.query.get(session['applied_coupon'])
            if coupon:
                discount_amount, _ = coupon.apply_coupon(current_user.id, subtotal)

        total = subtotal + tax_amount + shipping_amount - discount_amount

        # Create order
        order = Order(
            user_id=current_user.id,
            customer_email=form.email.data,
            customer_phone=form.phone.data,
            subtotal=subtotal,
            tax_amount=tax_amount,
            shipping_amount=shipping_amount,
            discount_amount=discount_amount,
            total_amount=total,
            billing_first_name=form.billing_first_name.data,
            billing_last_name=form.billing_last_name.data,
            billing_address_line1=form.billing_address_line1.data,
            billing_address_line2=form.billing_address_line2.data,
            billing_city=form.billing_city.data,
            billing_state=form.billing_state.data,
            billing_postal_code=form.billing_postal_code.data,
            billing_country=form.billing_country.data,
            shipping_first_name=form.shipping_first_name.data or form.billing_first_name.data,
            shipping_last_name=form.shipping_last_name.data or form.billing_last_name.data,
            shipping_address_line1=form.shipping_address_line1.data or form.billing_address_line1.data,
            shipping_address_line2=form.shipping_address_line2.data or form.billing_address_line2.data,
            shipping_city=form.shipping_city.data or form.billing_city.data,
            shipping_state=form.shipping_state.data or form.billing_state.data,
            shipping_postal_code=form.shipping_postal_code.data or form.billing_postal_code.data,
            shipping_country=form.shipping_country.data or form.billing_country.data,
            shipping_method=form.shipping_method.data,
            payment_method=form.payment_method.data,
            notes=form.notes.data,
            coupon_id=coupon.id if coupon else None
        )

        db.session.add(order)
        db.session.flush()  # Get order ID

        # Create order items
        for cart_item in current_user.cart.items:
            order_item = OrderItem(
                order_id=order.id,
                product_id=cart_item.product_id,
                quantity=cart_item.quantity,
                unit_price=cart_item.product.price,
                total_price=cart_item.get_total_price(),
                product_name=cart_item.product.name,
                product_sku=cart_item.product.sku,
                product_image=cart_item.product.main_image
            )
            db.session.add(order_item)

            # Reduce stock
            cart_item.product.reduce_stock(cart_item.quantity)

        # Increment coupon usage
        if coupon:
            coupon.increment_usage()

        # Clear cart
        current_user.cart.clear()

        # Remove applied coupon from session
        session.pop('applied_coupon', None)

        db.session.commit()

        flash(f'Order {order.order_number} placed successfully!', 'success')
        return redirect(url_for('cart.order_confirmation', order_id=order.id))

    # Pre-fill form with user data
    if request.method == 'GET':
        form.email.data = current_user.email
        form.phone.data = current_user.phone

        if current_user.address_line1:
            form.billing_first_name.data = current_user.first_name
            form.billing_last_name.data = current_user.last_name
            form.billing_address_line1.data = current_user.address_line1
            form.billing_address_line2.data = current_user.address_line2
            form.billing_city.data = current_user.city
            form.billing_state.data = current_user.state
            form.billing_postal_code.data = current_user.postal_code
            form.billing_country.data = current_user.country

    # Calculate totals for display
    subtotal = current_user.cart.get_subtotal()
    tax_amount = calculate_tax(subtotal)
    shipping_amount = calculate_shipping(current_user.cart.get_total_weight())

    # Apply coupon if in session
    discount_amount = 0
    coupon = None
    if 'applied_coupon' in session:
        coupon = Coupon.query.get(session['applied_coupon'])
        if coupon:
            discount_amount, _ = coupon.apply_coupon(current_user.id, subtotal)

    total = subtotal + tax_amount + shipping_amount - discount_amount

    return render_template('cart/checkout.html',
                         title='Checkout',
                         form=form,
                         coupon_form=coupon_form,
                         cart_items=current_user.cart.items.all(),
                         subtotal=subtotal,
                         tax_amount=tax_amount,
                         shipping_amount=shipping_amount,
                         discount_amount=discount_amount,
                         total=total,
                         applied_coupon=coupon)

@bp.route('/apply-coupon', methods=['POST'])
@login_required
def apply_coupon():
    form = CouponForm()
    
    if form.validate_on_submit():
        coupon = Coupon.query.filter_by(code=form.code.data.upper()).first()
        
        if not coupon:
            flash('Invalid coupon code.', 'error')
        else:
            subtotal = current_user.cart.get_subtotal()
            is_valid, message = coupon.is_valid(current_user.id, subtotal)
            
            if is_valid:
                session['applied_coupon'] = coupon.id
                flash(f'Coupon "{coupon.code}" applied successfully!', 'success')
            else:
                flash(message, 'error')
    
    return redirect(url_for('cart.checkout'))

@bp.route('/remove-coupon', methods=['POST'])
@login_required
def remove_coupon():
    session.pop('applied_coupon', None)
    flash('Coupon removed.', 'info')
    return redirect(url_for('cart.checkout'))

@bp.route('/api/cart/count')
def api_cart_count():
    """API endpoint to get cart count"""
    count = 0
    
    if current_user.is_authenticated and current_user.cart:
        count = current_user.cart.get_total_items()
    else:
        cart = session.get('cart', {})
        count = sum(cart.values())
    
    return jsonify({'count': count})

@bp.route('/api/cart/add', methods=['POST'])
def api_add_to_cart():
    """API endpoint to add items to cart"""
    data = request.get_json()
    product_id = data.get('product_id')
    quantity = data.get('quantity', 1)
    
    product = Product.query.get_or_404(product_id)
    
    if not product.is_active:
        return jsonify({'success': False, 'message': 'Product not available'})
    
    if product.track_inventory and quantity > product.stock_quantity:
        return jsonify({
            'success': False, 
            'message': f'Only {product.stock_quantity} items available'
        })
    
    if current_user.is_authenticated:
        if not current_user.cart:
            current_user.cart = Cart()
            db.session.add(current_user.cart)
        
        current_user.cart.add_item(product, quantity)
        cart_count = current_user.cart.get_total_items()
    else:
        cart = session.get('cart', {})
        product_id_str = str(product_id)
        
        if product_id_str in cart:
            cart[product_id_str] += quantity
        else:
            cart[product_id_str] = quantity
        
        session['cart'] = cart
        cart_count = sum(cart.values())
    
    return jsonify({
        'success': True,
        'message': f'{product.name} added to cart',
        'cart_count': cart_count
    })

@bp.route('/order-confirmation/<int:order_id>')
@login_required
def order_confirmation(order_id):
    from app.models.order import Order
    order = Order.query.filter_by(id=order_id, user_id=current_user.id).first_or_404()

    return render_template('cart/order_confirmation.html',
                         title='Order Confirmation',
                         order=order)
