from flask_wtf import F<PERSON>kForm
from wtforms import <PERSON>Field, PasswordField, BooleanField, SubmitField, TextAreaField, DateField, SelectField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError, Optional
from app.models.user import User
from app.utils.validators import validate_phone, validate_postal_code, validate_password_strength

class LoginForm(FlaskForm):
    username_or_email = StringField('Username or Email', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])
    remember_me = BooleanField('Remember Me')
    submit = SubmitField('Sign In')

class RegistrationForm(FlaskForm):
    username = <PERSON><PERSON><PERSON>('Username', validators=[
        DataRequired(), 
        Length(min=4, max=20, message='Username must be between 4 and 20 characters.')
    ])
    email = StringField('Email', validators=[DataRequired(), Email()])
    first_name = String<PERSON>ield('First Name', validators=[
        DataRequired(), 
        Length(min=2, max=50, message='First name must be between 2 and 50 characters.')
    ])
    last_name = <PERSON><PERSON>ield('Last Name', validators=[
        DataRequired(), 
        Length(min=2, max=50, message='Last name must be between 2 and 50 characters.')
    ])
    phone = StringField('Phone Number', validators=[Optional(), validate_phone])
    password = PasswordField('Password', validators=[
        DataRequired(), 
        validate_password_strength
    ])
    password2 = PasswordField('Repeat Password', validators=[
        DataRequired(), 
        EqualTo('password', message='Passwords must match.')
    ])
    submit = SubmitField('Register')
    
    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user:
            raise ValidationError('Username already exists. Please choose a different one.')
    
    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user:
            raise ValidationError('Email already registered. Please choose a different one.')

class ProfileForm(FlaskForm):
    first_name = StringField('First Name', validators=[
        DataRequired(), 
        Length(min=2, max=50)
    ])
    last_name = StringField('Last Name', validators=[
        DataRequired(), 
        Length(min=2, max=50)
    ])
    phone = StringField('Phone Number', validators=[Optional(), validate_phone])
    date_of_birth = DateField('Date of Birth', validators=[Optional()])
    address_line1 = StringField('Address Line 1', validators=[Optional(), Length(max=200)])
    address_line2 = StringField('Address Line 2', validators=[Optional(), Length(max=200)])
    city = StringField('City', validators=[Optional(), Length(max=100)])
    state = StringField('State/Province', validators=[Optional(), Length(max=100)])
    postal_code = StringField('Postal Code', validators=[Optional(), validate_postal_code])
    country = SelectField('Country', choices=[
        ('', 'Select Country'),
        ('US', 'United States'),
        ('CA', 'Canada'),
        ('UK', 'United Kingdom'),
        ('AU', 'Australia'),
        ('IN', 'India'),
        ('DE', 'Germany'),
        ('FR', 'France'),
        ('JP', 'Japan'),
        ('BR', 'Brazil'),
        ('MX', 'Mexico')
    ], validators=[Optional()])
    submit = SubmitField('Update Profile')

class ChangePasswordForm(FlaskForm):
    current_password = PasswordField('Current Password', validators=[DataRequired()])
    new_password = PasswordField('New Password', validators=[
        DataRequired(), 
        validate_password_strength
    ])
    new_password2 = PasswordField('Repeat New Password', validators=[
        DataRequired(), 
        EqualTo('new_password', message='Passwords must match.')
    ])
    submit = SubmitField('Change Password')
