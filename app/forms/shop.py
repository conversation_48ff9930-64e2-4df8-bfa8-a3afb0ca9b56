from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, SubmitField, IntegerField
from wtforms.validators import DataRequired, Length, NumberRange, Optional

class ReviewForm(FlaskForm):
    rating = SelectField('Rating', choices=[
        (5, '5 Stars - Excellent'),
        (4, '4 Stars - Very Good'),
        (3, '3 Stars - Good'),
        (2, '2 Stars - Fair'),
        (1, '1 Star - Poor')
    ], coerce=int, validators=[DataRequired()])
    
    title = StringField('Review Title', validators=[
        DataRequired(),
        Length(min=5, max=200, message='Title must be between 5 and 200 characters.')
    ])
    
    comment = TextAreaField('Review Comment', validators=[
        DataRequired(),
        Length(min=10, max=1000, message='Comment must be between 10 and 1000 characters.')
    ])
    
    submit = SubmitField('Submit Review')

class ProductSearchForm(FlaskForm):
    query = StringField('Search Products', validators=[DataRequired()])
    category = SelectField('Category', choices=[('', 'All Categories')], validators=[Optional()])
    min_price = IntegerField('Min Price', validators=[Optional(), NumberRange(min=0)])
    max_price = IntegerField('Max Price', validators=[Optional(), NumberRange(min=0)])
    sort_by = SelectField('Sort By', choices=[
        ('newest', 'Newest First'),
        ('price_low', 'Price: Low to High'),
        ('price_high', 'Price: High to Low'),
        ('name', 'Name A-Z'),
        ('featured', 'Featured')
    ], default='newest')
    
    submit = SubmitField('Search')
