from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON><PERSON>ield, FileAllowed, FileRequired
from wtforms import StringField, TextAreaField, DecimalField, IntegerField, SelectField, BooleanField, SubmitField, DateField
from wtforms.validators import DataRequired, Length, NumberRange, Optional, ValidationError
from app.models.product import Product, Category
from app.models.coupon import Coupon
from app.utils.validators import validate_sku

class CategoryForm(FlaskForm):
    name = StringField('Category Name', validators=[
        DataRequired(),
        Length(min=2, max=100, message='Name must be between 2 and 100 characters.')
    ])
    description = TextAreaField('Description', validators=[Optional(), Length(max=500)])
    image = FileField('Category Image', validators=[
        Optional(),
        FileAllowed(['jpg', 'png', 'jpeg', 'gif', 'webp'], 'Images only!')
    ])
    is_active = BooleanField('Active', default=True)
    sort_order = IntegerField('Sort Order', validators=[Optional(), NumberRange(min=0)], default=0)
    
    # SEO Fields
    meta_title = StringField('Meta Title', validators=[Optional(), Length(max=200)])
    meta_description = TextAreaField('Meta Description', validators=[Optional(), Length(max=300)])
    meta_keywords = StringField('Meta Keywords', validators=[Optional(), Length(max=500)])
    
    submit = SubmitField('Save Category')

class ProductForm(FlaskForm):
    name = StringField('Product Name', validators=[
        DataRequired(),
        Length(min=2, max=200, message='Name must be between 2 and 200 characters.')
    ])
    description = TextAreaField('Description', validators=[Optional()])
    short_description = TextAreaField('Short Description', validators=[
        Optional(),
        Length(max=500, message='Short description must be less than 500 characters.')
    ])
    
    # Pricing
    price = DecimalField('Price', validators=[
        DataRequired(),
        NumberRange(min=0.01, message='Price must be greater than 0.')
    ], places=2)
    compare_price = DecimalField('Compare Price', validators=[
        Optional(),
        NumberRange(min=0, message='Compare price must be positive.')
    ], places=2)
    cost_price = DecimalField('Cost Price', validators=[
        Optional(),
        NumberRange(min=0, message='Cost price must be positive.')
    ], places=2)
    
    # Inventory
    sku = StringField('SKU', validators=[DataRequired(), validate_sku])
    stock_quantity = IntegerField('Stock Quantity', validators=[
        Optional(),
        NumberRange(min=0, message='Stock quantity must be positive.')
    ], default=0)
    low_stock_threshold = IntegerField('Low Stock Threshold', validators=[
        Optional(),
        NumberRange(min=0, message='Threshold must be positive.')
    ], default=5)
    track_inventory = BooleanField('Track Inventory', default=True)
    
    # Product attributes
    weight = DecimalField('Weight (kg)', validators=[
        Optional(),
        NumberRange(min=0, message='Weight must be positive.')
    ], places=2)
    dimensions = StringField('Dimensions', validators=[Optional(), Length(max=100)])
    brand = StringField('Brand', validators=[Optional(), Length(max=100)])
    model = StringField('Model', validators=[Optional(), Length(max=100)])
    color = StringField('Color', validators=[Optional(), Length(max=50)])
    size = StringField('Size', validators=[Optional(), Length(max=50)])
    material = StringField('Material', validators=[Optional(), Length(max=100)])
    
    # Category
    category_id = SelectField('Category', coerce=int, validators=[DataRequired()])
    
    # Status
    is_active = BooleanField('Active', default=True)
    is_featured = BooleanField('Featured', default=False)
    is_digital = BooleanField('Digital Product', default=False)
    
    # SEO Fields
    meta_title = StringField('Meta Title', validators=[Optional(), Length(max=200)])
    meta_description = TextAreaField('Meta Description', validators=[Optional(), Length(max=300)])
    meta_keywords = StringField('Meta Keywords', validators=[Optional(), Length(max=500)])
    
    submit = SubmitField('Save Product')
    
    def __init__(self, *args, **kwargs):
        super(ProductForm, self).__init__(*args, **kwargs)
        self.category_id.choices = [(c.id, c.name) for c in Category.query.filter_by(is_active=True).order_by(Category.name).all()]
    
    def validate_sku(self, sku):
        product = Product.query.filter_by(sku=sku.data).first()
        if product and (not hasattr(self, 'product') or product.id != self.product.id):
            raise ValidationError('SKU already exists. Please choose a different one.')

class ProductImageForm(FlaskForm):
    image = FileField('Product Image', validators=[
        FileRequired(),
        FileAllowed(['jpg', 'png', 'jpeg', 'gif', 'webp'], 'Images only!')
    ])
    alt_text = StringField('Alt Text', validators=[Optional(), Length(max=200)])
    is_main = BooleanField('Main Image', default=False)
    sort_order = IntegerField('Sort Order', validators=[Optional(), NumberRange(min=0)], default=0)
    submit = SubmitField('Upload Image')

class CouponForm(FlaskForm):
    code = StringField('Coupon Code', validators=[
        DataRequired(),
        Length(min=3, max=50, message='Code must be between 3 and 50 characters.')
    ])
    name = StringField('Coupon Name', validators=[
        DataRequired(),
        Length(min=3, max=100, message='Name must be between 3 and 100 characters.')
    ])
    description = TextAreaField('Description', validators=[Optional(), Length(max=500)])
    
    # Discount configuration
    discount_type = SelectField('Discount Type', choices=[
        ('percentage', 'Percentage'),
        ('fixed', 'Fixed Amount')
    ], validators=[DataRequired()])
    discount_value = DecimalField('Discount Value', validators=[
        DataRequired(),
        NumberRange(min=0.01, message='Discount value must be greater than 0.')
    ], places=2)
    
    # Usage limits
    usage_limit = IntegerField('Total Usage Limit', validators=[
        Optional(),
        NumberRange(min=1, message='Usage limit must be at least 1.')
    ])
    usage_limit_per_user = IntegerField('Usage Limit Per User', validators=[
        Optional(),
        NumberRange(min=1, message='Per user limit must be at least 1.')
    ], default=1)
    
    # Requirements
    minimum_amount = DecimalField('Minimum Order Amount', validators=[
        Optional(),
        NumberRange(min=0, message='Minimum amount must be positive.')
    ], places=2)
    maximum_discount = DecimalField('Maximum Discount Amount', validators=[
        Optional(),
        NumberRange(min=0, message='Maximum discount must be positive.')
    ], places=2)
    
    # Validity period
    valid_from = DateField('Valid From', validators=[DataRequired()])
    valid_until = DateField('Valid Until', validators=[DataRequired()])
    
    # Status
    is_active = BooleanField('Active', default=True)
    
    submit = SubmitField('Save Coupon')
    
    def validate_code(self, code):
        coupon = Coupon.query.filter_by(code=code.data.upper()).first()
        if coupon and (not hasattr(self, 'coupon') or coupon.id != self.coupon.id):
            raise ValidationError('Coupon code already exists. Please choose a different one.')
    
    def validate_valid_until(self, valid_until):
        if self.valid_from.data and valid_until.data <= self.valid_from.data:
            raise ValidationError('End date must be after start date.')

class OrderStatusForm(FlaskForm):
    status = SelectField('Order Status', choices=[
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('processing', 'Processing'),
        ('shipped', 'Shipped'),
        ('delivered', 'Delivered'),
        ('cancelled', 'Cancelled'),
        ('refunded', 'Refunded')
    ], validators=[DataRequired()])
    tracking_number = StringField('Tracking Number', validators=[Optional(), Length(max=100)])
    admin_notes = TextAreaField('Admin Notes', validators=[Optional(), Length(max=500)])
    submit = SubmitField('Update Order')
