from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, BooleanField, SubmitField, TextAreaField
from wtforms.validators import DataRequired, Email, Length, Optional
from app.utils.validators import validate_phone, validate_postal_code

class CheckoutForm(FlaskForm):
    # Customer Information
    email = StringField('Email', validators=[DataRequired(), Email()])
    phone = StringField('Phone', validators=[DataRequired(), validate_phone])
    
    # Billing Address
    billing_first_name = StringField('First Name', validators=[DataRequired(), Length(max=50)])
    billing_last_name = <PERSON>Field('Last Name', validators=[DataRequired(), Length(max=50)])
    billing_address_line1 = StringField('Address Line 1', validators=[DataRequired(), Length(max=200)])
    billing_address_line2 = StringField('Address Line 2', validators=[Optional(), Length(max=200)])
    billing_city = StringField('City', validators=[DataRequired(), Length(max=100)])
    billing_state = StringField('State/Province', validators=[DataRequired(), Length(max=100)])
    billing_postal_code = StringField('Postal Code', validators=[DataRequired(), validate_postal_code])
    billing_country = SelectField('Country', choices=[
        ('US', 'United States'),
        ('CA', 'Canada'),
        ('UK', 'United Kingdom'),
        ('AU', 'Australia'),
        ('IN', 'India'),
        ('DE', 'Germany'),
        ('FR', 'France'),
        ('JP', 'Japan'),
        ('BR', 'Brazil'),
        ('MX', 'Mexico')
    ], validators=[DataRequired()])
    
    # Shipping Address
    same_as_billing = BooleanField('Shipping address same as billing', default=True)
    shipping_first_name = StringField('First Name', validators=[Optional(), Length(max=50)])
    shipping_last_name = StringField('Last Name', validators=[Optional(), Length(max=50)])
    shipping_address_line1 = StringField('Address Line 1', validators=[Optional(), Length(max=200)])
    shipping_address_line2 = StringField('Address Line 2', validators=[Optional(), Length(max=200)])
    shipping_city = StringField('City', validators=[Optional(), Length(max=100)])
    shipping_state = StringField('State/Province', validators=[Optional(), Length(max=100)])
    shipping_postal_code = StringField('Postal Code', validators=[Optional()])
    shipping_country = SelectField('Country', choices=[
        ('US', 'United States'),
        ('CA', 'Canada'),
        ('UK', 'United Kingdom'),
        ('AU', 'Australia'),
        ('IN', 'India'),
        ('DE', 'Germany'),
        ('FR', 'France'),
        ('JP', 'Japan'),
        ('BR', 'Brazil'),
        ('MX', 'Mexico')
    ], validators=[Optional()])
    
    # Shipping Method
    shipping_method = SelectField('Shipping Method', choices=[
        ('standard', 'Standard Shipping (5-7 business days) - $5.99'),
        ('express', 'Express Shipping (2-3 business days) - $12.99'),
        ('overnight', 'Overnight Shipping (1 business day) - $24.99')
    ], validators=[DataRequired()])
    
    # Payment Method
    payment_method = SelectField('Payment Method', choices=[
        ('credit_card', 'Credit Card'),
        ('paypal', 'PayPal'),
        ('bank_transfer', 'Bank Transfer'),
        ('cash_on_delivery', 'Cash on Delivery')
    ], validators=[DataRequired()])
    
    # Order Notes
    notes = TextAreaField('Order Notes', validators=[Optional(), Length(max=500)])
    
    # Terms and Conditions
    terms_accepted = BooleanField('I accept the terms and conditions', validators=[DataRequired()])
    
    submit = SubmitField('Place Order')

class CouponForm(FlaskForm):
    code = StringField('Coupon Code', validators=[DataRequired(), Length(max=50)])
    submit = SubmitField('Apply Coupon')
