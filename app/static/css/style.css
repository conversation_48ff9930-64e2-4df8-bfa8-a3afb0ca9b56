/* Custom CSS for FlaskShop */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.15s ease-in-out;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

.btn {
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.card {
    border: none;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* Navigation */
.navbar-brand {
    font-size: 1.5rem;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-result-item {
    padding: 0.75rem;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: var(--transition);
}

.search-result-item:hover {
    background-color: var(--light-color);
}

.search-result-item:last-child {
    border-bottom: none;
}

/* Product Cards */
.product-card {
    height: 100%;
    transition: var(--transition);
}

.product-card .card-img-top {
    height: 250px;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .card-img-top {
    transform: scale(1.05);
}

.product-price {
    font-size: 1.25rem;
    font-weight: bold;
    color: var(--primary-color);
}

.product-compare-price {
    text-decoration: line-through;
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.product-discount {
    background-color: var(--danger-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
}

.product-rating {
    color: #ffc107;
}

.product-stock-status {
    font-size: 0.9rem;
}

.stock-in {
    color: var(--success-color);
}

.stock-low {
    color: var(--warning-color);
}

.stock-out {
    color: var(--danger-color);
}

/* Product Detail */
.product-gallery {
    position: sticky;
    top: 2rem;
}

.product-main-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.product-thumbnail {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--border-radius);
    cursor: pointer;
    opacity: 0.7;
    transition: var(--transition);
}

.product-thumbnail:hover,
.product-thumbnail.active {
    opacity: 1;
    border: 2px solid var(--primary-color);
}

.quantity-input {
    max-width: 100px;
}

/* Cart */
.cart-item {
    border-bottom: 1px solid #eee;
    padding: 1rem 0;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.cart-summary {
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
}

/* Checkout */
.checkout-step {
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.checkout-step.active {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.1);
}

/* Reviews */
.review-item {
    border-bottom: 1px solid #eee;
    padding: 1rem 0;
}

.review-item:last-child {
    border-bottom: none;
}

.review-rating {
    color: #ffc107;
    margin-bottom: 0.5rem;
}

.review-meta {
    font-size: 0.9rem;
    color: var(--secondary-color);
}

/* Wishlist */
.wishlist-btn {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.wishlist-btn:hover {
    background: white;
    color: var(--danger-color);
}

.wishlist-btn.active {
    color: var(--danger-color);
}

/* Admin Panel */
.admin-sidebar {
    background-color: var(--dark-color);
    min-height: calc(100vh - 76px);
}

.admin-sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 0.25rem;
}

.admin-sidebar .nav-link:hover,
.admin-sidebar .nav-link.active {
    color: white;
    background-color: var(--primary-color);
}

.admin-stat-card {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
}

.admin-stat-icon {
    font-size: 2rem;
    opacity: 0.8;
}

/* Forms */
.form-floating > label {
    color: var(--secondary-color);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Pagination */
.pagination .page-link {
    color: var(--primary-color);
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Filters */
.filter-sidebar {
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
}

.filter-group {
    margin-bottom: 1.5rem;
}

.filter-group h6 {
    margin-bottom: 0.75rem;
    font-weight: 600;
}

/* Loading Spinner */
.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .product-main-image {
        height: 300px;
    }
    
    .cart-item-image {
        width: 60px;
        height: 60px;
    }
    
    .admin-sidebar {
        min-height: auto;
    }
    
    .search-results {
        position: fixed;
        top: 76px;
        left: 1rem;
        right: 1rem;
        max-height: 50vh;
    }
}

@media (max-width: 576px) {
    .product-card .card-img-top {
        height: 200px;
    }
    
    .product-main-image {
        height: 250px;
    }
    
    .cart-summary {
        margin-top: 2rem;
    }
}

/* Utilities */
.text-primary { color: var(--primary-color) !important; }
.bg-primary { background-color: var(--primary-color) !important; }
.border-primary { border-color: var(--primary-color) !important; }

.cursor-pointer { cursor: pointer; }
.user-select-none { user-select: none; }

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Print Styles */
@media print {
    .navbar, .footer, .btn, .pagination {
        display: none !important;
    }
    
    .container {
        max-width: none !important;
    }
}
