// Main JavaScript for FlaskShop

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initializeSearch();
    initializeCart();
    initializeWishlist();
    initializeProductGallery();
    initializeQuantityControls();
    initializeFormValidation();
    initializeTooltips();
    initializeModals();
});

// Search functionality
function initializeSearch() {
    const searchInput = document.getElementById('searchInput');
    const searchResults = document.getElementById('searchResults');
    let searchTimeout;

    if (searchInput && searchResults) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length < 2) {
                searchResults.style.display = 'none';
                return;
            }

            searchTimeout = setTimeout(() => {
                fetch(`/api/search?q=${encodeURIComponent(query)}&limit=5`)
                    .then(response => response.json())
                    .then(data => {
                        displaySearchResults(data, searchResults);
                    })
                    .catch(error => {
                        console.error('Search error:', error);
                    });
            }, 300);
        });

        // Hide search results when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
                searchResults.style.display = 'none';
            }
        });
    }
}

function displaySearchResults(products, container) {
    if (products.length === 0) {
        container.innerHTML = '<div class="search-result-item">No products found</div>';
    } else {
        container.innerHTML = products.map(product => `
            <div class="search-result-item" onclick="window.location.href='/shop/product/${product.slug}'">
                <div class="d-flex align-items-center">
                    <img src="/static/uploads/products/${product.image}" alt="${product.name}" 
                         class="me-3" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
                    <div>
                        <div class="fw-medium">${product.name}</div>
                        <div class="text-muted small">$${product.price.toFixed(2)} - ${product.category}</div>
                    </div>
                </div>
            </div>
        `).join('');
    }
    container.style.display = 'block';
}

// Cart functionality
function initializeCart() {
    // Add to cart buttons
    document.addEventListener('click', function(e) {
        if (e.target.matches('.add-to-cart-btn') || e.target.closest('.add-to-cart-btn')) {
            e.preventDefault();
            const btn = e.target.matches('.add-to-cart-btn') ? e.target : e.target.closest('.add-to-cart-btn');
            addToCart(btn);
        }
    });

    // Update cart quantity
    document.addEventListener('change', function(e) {
        if (e.target.matches('.cart-quantity-input')) {
            updateCartQuantity(e.target);
        }
    });

    // Remove from cart
    document.addEventListener('click', function(e) {
        if (e.target.matches('.remove-from-cart') || e.target.closest('.remove-from-cart')) {
            e.preventDefault();
            const btn = e.target.matches('.remove-from-cart') ? e.target : e.target.closest('.remove-from-cart');
            removeFromCart(btn);
        }
    });
}

function addToCart(button) {
    const productId = button.dataset.productId;
    const quantityInput = document.querySelector(`input[name="quantity"]`);
    const quantity = quantityInput ? parseInt(quantityInput.value) : 1;

    // Show loading state
    const originalText = button.innerHTML;
    button.innerHTML = '<span class="spinner"></span> Adding...';
    button.disabled = true;

    fetch('/api/cart/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: parseInt(productId),
            quantity: quantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateCartCount(data.cart_count);
            showNotification('Product added to cart!', 'success');
        } else {
            showNotification(data.message || 'Error adding to cart', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error adding to cart', 'error');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function updateCartQuantity(input) {
    const productId = input.dataset.productId;
    const quantity = parseInt(input.value);

    fetch('/api/cart/update', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: parseInt(productId),
            quantity: quantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateCartCount(data.cart.total_items);
            updateCartTotals(data.cart);
        } else {
            showNotification(data.message || 'Error updating cart', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error updating cart', 'error');
    });
}

function removeFromCart(button) {
    const productId = button.dataset.productId;

    if (confirm('Remove this item from cart?')) {
        fetch('/api/cart/remove', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                product_id: parseInt(productId)
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                button.closest('.cart-item').remove();
                updateCartCount(data.cart.total_items);
                updateCartTotals(data.cart);
                showNotification('Item removed from cart', 'info');
            } else {
                showNotification(data.message || 'Error removing item', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error removing item', 'error');
        });
    }
}

function updateCartCount(count) {
    const cartCountElements = document.querySelectorAll('#cartCount');
    cartCountElements.forEach(element => {
        element.textContent = count || 0;
        element.style.display = count > 0 ? 'inline' : 'none';
    });
}

function updateCartTotals(cart) {
    const subtotalElement = document.getElementById('cartSubtotal');
    const totalElement = document.getElementById('cartTotal');
    
    if (subtotalElement) {
        subtotalElement.textContent = `$${cart.subtotal.toFixed(2)}`;
    }
    
    if (totalElement) {
        // This would need to be calculated with tax and shipping
        totalElement.textContent = `$${cart.subtotal.toFixed(2)}`;
    }
}

// Wishlist functionality
function initializeWishlist() {
    document.addEventListener('click', function(e) {
        if (e.target.matches('.wishlist-btn') || e.target.closest('.wishlist-btn')) {
            e.preventDefault();
            const btn = e.target.matches('.wishlist-btn') ? e.target : e.target.closest('.wishlist-btn');
            toggleWishlist(btn);
        }
    });
}

function toggleWishlist(button) {
    const productId = button.dataset.productId;
    const icon = button.querySelector('i');

    fetch('/api/wishlist/toggle', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: parseInt(productId)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.action === 'added') {
            icon.classList.remove('far');
            icon.classList.add('fas');
            button.classList.add('active');
        } else {
            icon.classList.remove('fas');
            icon.classList.add('far');
            button.classList.remove('active');
        }
        showNotification(data.message, 'success');
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error updating wishlist', 'error');
    });
}

// Product gallery
function initializeProductGallery() {
    const thumbnails = document.querySelectorAll('.product-thumbnail');
    const mainImage = document.getElementById('productMainImage');

    thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
            // Remove active class from all thumbnails
            thumbnails.forEach(t => t.classList.remove('active'));
            
            // Add active class to clicked thumbnail
            this.classList.add('active');
            
            // Update main image
            if (mainImage) {
                mainImage.src = this.src;
                mainImage.alt = this.alt;
            }
        });
    });
}

// Quantity controls
function initializeQuantityControls() {
    document.addEventListener('click', function(e) {
        if (e.target.matches('.quantity-btn-minus')) {
            e.preventDefault();
            const input = e.target.nextElementSibling;
            const currentValue = parseInt(input.value);
            if (currentValue > 1) {
                input.value = currentValue - 1;
                input.dispatchEvent(new Event('change'));
            }
        }
        
        if (e.target.matches('.quantity-btn-plus')) {
            e.preventDefault();
            const input = e.target.previousElementSibling;
            const currentValue = parseInt(input.value);
            const maxValue = parseInt(input.max) || 999;
            if (currentValue < maxValue) {
                input.value = currentValue + 1;
                input.dispatchEvent(new Event('change'));
            }
        }
    });
}

// Form validation
function initializeFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!form.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
}

// Initialize tooltips
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Initialize modals
function initializeModals() {
    // Auto-focus first input in modals
    document.addEventListener('shown.bs.modal', function(e) {
        const firstInput = e.target.querySelector('input, textarea, select');
        if (firstInput) {
            firstInput.focus();
        }
    });
}

// Notification system
function showNotification(message, type = 'info') {
    const alertClass = type === 'error' ? 'alert-danger' : `alert-${type}`;
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', alertHtml);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        const alert = document.querySelector('.alert.position-fixed');
        if (alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }
    }, 5000);
}

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Export functions for use in other scripts
window.FlaskShop = {
    addToCart,
    toggleWishlist,
    showNotification,
    formatCurrency,
    debounce
};
