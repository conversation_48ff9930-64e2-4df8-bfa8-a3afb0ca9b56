#!/bin/bash

# Flask E-commerce Application Startup Script

echo "🚀 Starting Flask E-commerce Application..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install dependencies if requirements.txt exists
if [ -f "requirements.txt" ]; then
    echo "📚 Installing dependencies..."
    pip install -r requirements.txt
fi

# Check if database needs initialization
if [ ! -f "database_initialized.flag" ]; then
    echo "🗄️  Initializing database..."
    python init_db.py
    touch database_initialized.flag
    echo "✅ Database initialized successfully!"
fi

# Start the Flask application
echo "🌐 Starting Flask application..."
echo "📍 Application will be available at: http://localhost:5000"
echo "👤 Admin login: <EMAIL> / admin123"
echo "👤 User login: <EMAIL> / password123"
echo ""
echo "Press Ctrl+C to stop the application"
echo ""

python app.py
