#!/bin/bash

# Flask E-commerce Application Startup Script

echo "🚀 Starting Flask E-commerce Application..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install dependencies if requirements.txt exists
if [ -f "requirements.txt" ]; then
    echo "📚 Installing dependencies..."
    pip install -r requirements.txt
fi

# Test if Flask is working
echo "🧪 Testing Flask setup..."
if python quick_start.py --help > /dev/null 2>&1; then
    echo "✅ Flask setup is working!"
else
    echo "❌ Flask setup issue detected. Running simple demo instead..."
    echo "🌐 Starting simple demo application..."
    echo "📍 Application will be available at: http://localhost:5000"
    echo ""
    python simple_app.py
    exit 0
fi

# Check if database needs initialization
if [ ! -f "database_initialized.flag" ]; then
    echo "🗄️  Initializing database..."
    if python init_db.py; then
        touch database_initialized.flag
        echo "✅ Database initialized successfully!"
    else
        echo "⚠️  Database initialization failed. Running simple demo instead..."
        echo "🌐 Starting simple demo application..."
        echo "📍 Application will be available at: http://localhost:5000"
        echo ""
        python simple_app.py
        exit 0
    fi
fi

# Try to start the full Flask application
echo "🌐 Starting full Flask application..."
echo "📍 Application will be available at: http://localhost:5000"
echo "👤 Admin login: <EMAIL> / admin123"
echo "👤 User login: <EMAIL> / password123"
echo ""
echo "Press Ctrl+C to stop the application"
echo ""

if python app.py; then
    echo "✅ Application started successfully!"
else
    echo "❌ Full application failed to start. Trying simple demo..."
    echo "🌐 Starting simple demo application..."
    python simple_app.py
fi
