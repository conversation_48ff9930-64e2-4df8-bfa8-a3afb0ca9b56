#!/usr/bin/env python3
"""
Quick start script for Flask E-commerce Application
This script creates a minimal working version to test the setup
"""

from flask import Flask, render_template_string
import os

app = Flask(__name__)
app.secret_key = 'demo-secret-key'

# Simple template
TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>FlaskShop - Setup Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="alert alert-success">
                    <h1 class="alert-heading">🎉 Flask Setup Successful!</h1>
                    <p>Your Flask environment is working correctly. The dependencies have been installed and the basic Flask application is running.</p>
                    <hr>
                    <h4>Next Steps:</h4>
                    <ol>
                        <li><strong>Run the simple demo:</strong> <code>python simple_app.py</code></li>
                        <li><strong>Set up the full application:</strong> <code>./run.sh</code></li>
                        <li><strong>Initialize database:</strong> <code>python init_db.py</code></li>
                    </ol>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5>Available Applications</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Simple Demo</h6>
                                <p>Quick demo without database setup</p>
                                <code>python simple_app.py</code>
                            </div>
                            <div class="col-md-6">
                                <h6>Full E-commerce Site</h6>
                                <p>Complete application with database</p>
                                <code>./run.sh</code>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>Flask Course</h5>
                    </div>
                    <div class="card-body">
                        <p>Learn Flask from beginner to master level with our comprehensive course!</p>
                        <p>Course materials are available in the <code>flask-course/</code> directory.</p>
                        <p>Start with: <code>flask-course/lessons/01-introduction/README.md</code></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
'''

@app.route('/')
def home():
    return render_template_string(TEMPLATE)

if __name__ == '__main__':
    print("🚀 Flask Setup Test")
    print("📍 Open your browser and go to: http://localhost:5000")
    print("✅ If you see a success page, your Flask environment is ready!")
    print("-" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
