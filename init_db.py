#!/usr/bin/env python3
"""
Database initialization script for FlaskShop
This script creates the database tables and populates them with sample data
"""

from app import create_app
from app.extensions import db
from app.models.user import User
from app.models.product import Product, Category, ProductImage
from app.models.coupon import Coupon
from datetime import datetime, date, timedelta
import os

def create_admin_user():
    """Create default admin user"""
    admin = User.query.filter_by(email='<EMAIL>').first()
    if not admin:
        admin = User(
            username='admin',
            email='<EMAIL>',
            first_name='Admin',
            last_name='User',
            phone='+1234567890',
            is_admin=True,
            is_active=True,
            email_verified=True
        )
        admin.set_password('admin123')
        db.session.add(admin)
        print("✓ Created admin user (<EMAIL> / admin123)")
    else:
        print("✓ Admin user already exists")

def create_sample_categories():
    """Create sample product categories"""
    categories_data = [
        {
            'name': 'Electronics',
            'description': 'Latest gadgets and electronic devices',
            'sort_order': 1
        },
        {
            'name': 'Clothing',
            'description': 'Fashion and apparel for all occasions',
            'sort_order': 2
        },
        {
            'name': 'Home & Garden',
            'description': 'Everything for your home and garden',
            'sort_order': 3
        },
        {
            'name': 'Sports & Outdoors',
            'description': 'Sports equipment and outdoor gear',
            'sort_order': 4
        },
        {
            'name': 'Books',
            'description': 'Books, magazines, and educational materials',
            'sort_order': 5
        },
        {
            'name': 'Health & Beauty',
            'description': 'Health, beauty, and personal care products',
            'sort_order': 6
        }
    ]
    
    for cat_data in categories_data:
        category = Category.query.filter_by(name=cat_data['name']).first()
        if not category:
            category = Category(
                name=cat_data['name'],
                slug=cat_data['name'].lower().replace(' ', '-').replace('&', 'and'),
                description=cat_data['description'],
                sort_order=cat_data['sort_order'],
                is_active=True,
                meta_title=f"{cat_data['name']} - FlaskShop",
                meta_description=cat_data['description']
            )
            db.session.add(category)
    
    db.session.commit()
    print("✓ Created sample categories")

def create_sample_products():
    """Create sample products"""
    categories = {cat.name: cat for cat in Category.query.all()}
    
    products_data = [
        {
            'name': 'Wireless Bluetooth Headphones',
            'category': 'Electronics',
            'price': 79.99,
            'compare_price': 99.99,
            'sku': 'WBH-001',
            'stock_quantity': 50,
            'description': 'High-quality wireless Bluetooth headphones with noise cancellation and long battery life.',
            'short_description': 'Premium wireless headphones with excellent sound quality.',
            'is_featured': True
        },
        {
            'name': 'Smartphone Case',
            'category': 'Electronics',
            'price': 24.99,
            'sku': 'SPC-001',
            'stock_quantity': 100,
            'description': 'Durable smartphone case with shock absorption and wireless charging compatibility.',
            'short_description': 'Protective case for your smartphone.'
        },
        {
            'name': 'Cotton T-Shirt',
            'category': 'Clothing',
            'price': 19.99,
            'compare_price': 29.99,
            'sku': 'CTS-001',
            'stock_quantity': 75,
            'description': '100% cotton t-shirt, comfortable and breathable. Available in multiple colors.',
            'short_description': 'Comfortable cotton t-shirt for everyday wear.',
            'is_featured': True
        },
        {
            'name': 'Denim Jeans',
            'category': 'Clothing',
            'price': 59.99,
            'sku': 'DJ-001',
            'stock_quantity': 30,
            'description': 'Classic denim jeans with a modern fit. Made from premium denim fabric.',
            'short_description': 'Stylish denim jeans with perfect fit.'
        },
        {
            'name': 'Coffee Maker',
            'category': 'Home & Garden',
            'price': 89.99,
            'compare_price': 119.99,
            'sku': 'CM-001',
            'stock_quantity': 25,
            'description': 'Programmable coffee maker with 12-cup capacity and auto-shutoff feature.',
            'short_description': 'Programmable coffee maker for perfect coffee every time.',
            'is_featured': True
        },
        {
            'name': 'Garden Tools Set',
            'category': 'Home & Garden',
            'price': 45.99,
            'sku': 'GTS-001',
            'stock_quantity': 40,
            'description': 'Complete garden tools set including trowel, pruner, and watering can.',
            'short_description': 'Essential tools for your garden.'
        },
        {
            'name': 'Running Shoes',
            'category': 'Sports & Outdoors',
            'price': 89.99,
            'compare_price': 109.99,
            'sku': 'RS-001',
            'stock_quantity': 60,
            'description': 'Lightweight running shoes with excellent cushioning and breathable mesh upper.',
            'short_description': 'Comfortable running shoes for all terrains.'
        },
        {
            'name': 'Yoga Mat',
            'category': 'Sports & Outdoors',
            'price': 29.99,
            'sku': 'YM-001',
            'stock_quantity': 80,
            'description': 'Non-slip yoga mat with extra thickness for comfort during workouts.',
            'short_description': 'Premium yoga mat for your fitness routine.',
            'is_featured': True
        },
        {
            'name': 'Programming Book',
            'category': 'Books',
            'price': 39.99,
            'sku': 'PB-001',
            'stock_quantity': 35,
            'description': 'Comprehensive guide to modern programming languages and best practices.',
            'short_description': 'Learn programming with this comprehensive guide.'
        },
        {
            'name': 'Face Moisturizer',
            'category': 'Health & Beauty',
            'price': 24.99,
            'sku': 'FM-001',
            'stock_quantity': 90,
            'description': 'Hydrating face moisturizer with SPF protection and natural ingredients.',
            'short_description': 'Daily moisturizer with SPF protection.'
        }
    ]
    
    for prod_data in products_data:
        product = Product.query.filter_by(sku=prod_data['sku']).first()
        if not product:
            category = categories.get(prod_data['category'])
            if category:
                product = Product(
                    name=prod_data['name'],
                    slug=prod_data['name'].lower().replace(' ', '-'),
                    category_id=category.id,
                    price=prod_data['price'],
                    compare_price=prod_data.get('compare_price'),
                    sku=prod_data['sku'],
                    stock_quantity=prod_data['stock_quantity'],
                    description=prod_data['description'],
                    short_description=prod_data['short_description'],
                    is_active=True,
                    is_featured=prod_data.get('is_featured', False),
                    track_inventory=True,
                    low_stock_threshold=5,
                    meta_title=f"{prod_data['name']} - FlaskShop",
                    meta_description=prod_data['short_description']
                )
                db.session.add(product)
    
    db.session.commit()
    print("✓ Created sample products")

def create_sample_coupons():
    """Create sample coupons"""
    coupons_data = [
        {
            'code': 'WELCOME10',
            'name': 'Welcome Discount',
            'description': '10% off for new customers',
            'discount_type': 'percentage',
            'discount_value': 10,
            'minimum_amount': 50,
            'valid_from': date.today(),
            'valid_until': date.today() + timedelta(days=30),
            'usage_limit': 100,
            'usage_limit_per_user': 1
        },
        {
            'code': 'SAVE20',
            'name': 'Save $20',
            'description': '$20 off on orders over $100',
            'discount_type': 'fixed',
            'discount_value': 20,
            'minimum_amount': 100,
            'valid_from': date.today(),
            'valid_until': date.today() + timedelta(days=60),
            'usage_limit': 50,
            'usage_limit_per_user': 1
        },
        {
            'code': 'FREESHIP',
            'name': 'Free Shipping',
            'description': 'Free shipping on all orders',
            'discount_type': 'fixed',
            'discount_value': 5.99,
            'minimum_amount': 25,
            'valid_from': date.today(),
            'valid_until': date.today() + timedelta(days=90),
            'usage_limit': None,
            'usage_limit_per_user': 5
        }
    ]
    
    for coupon_data in coupons_data:
        coupon = Coupon.query.filter_by(code=coupon_data['code']).first()
        if not coupon:
            coupon = Coupon(**coupon_data)
            db.session.add(coupon)
    
    db.session.commit()
    print("✓ Created sample coupons")

def create_sample_user():
    """Create a sample regular user"""
    user = User.query.filter_by(email='<EMAIL>').first()
    if not user:
        user = User(
            username='testuser',
            email='<EMAIL>',
            first_name='John',
            last_name='Doe',
            phone='+1987654321',
            address_line1='123 Main St',
            city='Anytown',
            state='CA',
            postal_code='12345',
            country='US',
            is_active=True,
            email_verified=True
        )
        user.set_password('password123')
        db.session.add(user)
        print("✓ Created sample user (<EMAIL> / password123)")
    else:
        print("✓ Sample user already exists")

def main():
    """Main function to initialize the database"""
    app = create_app()
    
    with app.app_context():
        print("Initializing FlaskShop database...")
        
        # Create all tables
        db.create_all()
        print("✓ Created database tables")
        
        # Create sample data
        create_admin_user()
        create_sample_user()
        create_sample_categories()
        create_sample_products()
        create_sample_coupons()
        
        # Commit all changes
        db.session.commit()
        
        print("\n🎉 Database initialization completed successfully!")
        print("\nYou can now run the application with: python app.py")
        print("\nDefault accounts:")
        print("  Admin: <EMAIL> / admin123")
        print("  User:  <EMAIL> / password123")

if __name__ == '__main__':
    main()
