# FlaskShop - Complete E-commerce Website

A comprehensive e-commerce website built with Flask, featuring a complete shopping experience with admin panel, user authentication, cart management, order processing, and more.

## 🌟 Features

### Customer Features
- **User Authentication**: Registration, login, logout with secure password hashing
- **Product Browsing**: Category-based navigation, search, filtering, and sorting
- **Shopping Cart**: Add/remove items, quantity management, persistent cart
- **Checkout Process**: Multi-step checkout with address and payment information
- **Order Management**: Order history, tracking, and status updates
- **Wishlist**: Save favorite products for later
- **Product Reviews**: Rate and review products
- **Coupon System**: Apply discount coupons during checkout
- **Responsive Design**: Mobile-friendly interface

### Admin Features
- **Admin Dashboard**: Comprehensive analytics and overview
- **Product Management**: CRUD operations with multiple image support
- **Category Management**: Organize products into categories
- **Order Management**: View, update, and track orders
- **User Management**: View customer information and order history
- **Coupon Management**: Create and manage discount coupons
- **Review Moderation**: Approve or reject product reviews
- **Inventory Tracking**: Stock management and low stock alerts

### Technical Features
- **SEO Optimized**: Meta tags, structured data, and clean URLs
- **Security**: CSRF protection, secure sessions, input validation
- **Performance**: Optimized queries, caching, and image handling
- **API Endpoints**: RESTful API for frontend interactions
- **Email Integration**: Order confirmations and notifications
- **File Uploads**: Secure image upload and processing

## 🛠️ Technology Stack

- **Backend**: Flask (Python)
- **Database**: MySQL with SQLAlchemy ORM
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Authentication**: Flask-Login with bcrypt password hashing
- **Forms**: WTForms with validation
- **Email**: Flask-Mail
- **File Handling**: Pillow for image processing
- **Development**: Flask-Migrate for database migrations

## 📋 Prerequisites

- Python 3.8 or higher
- MySQL 5.7 or higher
- pip (Python package manager)
- Virtual environment (recommended)

## 🚀 Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd flask-ecom
```

### 2. Create Virtual Environment
```bash
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Database Setup

#### Create MySQL Database
```sql
CREATE DATABASE flask_ecommerce;
CREATE USER 'flask_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON flask_ecommerce.* TO 'flask_user'@'localhost';
FLUSH PRIVILEGES;
```

#### Configure Environment Variables
Create a `.env` file based on `.env.example`:
```bash
cp .env.example .env
```

Edit `.env` with your database credentials:
```
DATABASE_URL=mysql+pymysql://flask_user:your_password@localhost/flask_ecommerce
SECRET_KEY=your-secret-key-here
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
```

### 5. Initialize Database
```bash
python init_db.py
```

This will create all tables and populate them with sample data.

### 6. Run the Application
```bash
python app.py
```

The application will be available at `http://localhost:5000`

## 👤 Default Accounts

After running `init_db.py`, you can use these accounts:

**Admin Account:**
- Email: `<EMAIL>`
- Password: `admin123`

**Customer Account:**
- Email: `<EMAIL>`
- Password: `password123`

## 📁 Project Structure

```
flask-ecom/
├── app.py                 # Main application file
├── config.py             # Configuration settings
├── init_db.py           # Database initialization script
├── requirements.txt     # Python dependencies
├── .env.example        # Environment variables template
├── app/
│   ├── __init__.py
│   ├── models/         # Database models
│   │   ├── user.py
│   │   ├── product.py
│   │   ├── order.py
│   │   ├── cart.py
│   │   ├── coupon.py
│   │   ├── review.py
│   │   └── wishlist.py
│   ├── routes/         # Application routes
│   │   ├── main.py
│   │   ├── auth.py
│   │   ├── shop.py
│   │   ├── cart.py
│   │   ├── admin.py
│   │   └── api.py
│   ├── forms/          # WTForms
│   │   ├── auth.py
│   │   ├── shop.py
│   │   ├── cart.py
│   │   └── admin.py
│   ├── templates/      # Jinja2 templates
│   │   ├── base.html
│   │   ├── main/
│   │   ├── auth/
│   │   ├── shop/
│   │   ├── cart/
│   │   ├── admin/
│   │   └── components/
│   ├── static/         # Static files
│   │   ├── css/
│   │   ├── js/
│   │   ├── images/
│   │   └── uploads/
│   └── utils/          # Utility functions
│       ├── helpers.py
│       ├── decorators.py
│       └── validators.py
└── flask-course/       # Complete Flask learning course
    ├── README.md
    ├── requirements.txt
    └── lessons/
```

## 🎓 Flask Learning Course

This project includes a comprehensive Flask course that takes you from beginner to advanced level:

### Course Structure
- **30 Lessons** covering all Flask concepts
- **Hands-on Examples** with practical code
- **Real-world Projects** including this e-commerce site
- **Progressive Learning** from basics to advanced topics

### Key Topics Covered
- Flask fundamentals and routing
- Templates with Jinja2
- Forms and validation with WTForms
- **Sessions and Cookies** (detailed coverage)
- Database integration with SQLAlchemy
- User authentication and authorization
- **HTTP Methods and Request Handling**
- Security best practices
- Testing and deployment

### Getting Started with the Course
```bash
cd flask-course
pip install -r requirements.txt
```

Start with [Lesson 1: Introduction to Flask](./flask-course/lessons/01-introduction/README.md)

## 🔧 Configuration

### Environment Variables
- `DATABASE_URL`: Database connection string
- `SECRET_KEY`: Flask secret key for sessions
- `MAIL_SERVER`: SMTP server for emails
- `MAIL_USERNAME`: Email username
- `MAIL_PASSWORD`: Email password
- `SITE_URL`: Base URL for the site

### Application Settings
- Debug mode: Enabled in development
- Session timeout: 7 days for permanent sessions
- File upload limits: 16MB maximum
- Pagination: 12 products per page

## 🧪 Testing

Run the test suite:
```bash
python -m pytest tests/
```

## 📱 API Endpoints

The application provides RESTful API endpoints:

- `GET /api/products` - List products with filtering
- `GET /api/products/<id>` - Get product details
- `POST /api/cart/add` - Add item to cart
- `GET /api/categories` - List categories
- `POST /api/wishlist/toggle` - Toggle wishlist item

## 🔒 Security Features

- CSRF protection on all forms
- Secure password hashing with bcrypt
- Session security with secure cookies
- Input validation and sanitization
- SQL injection prevention with SQLAlchemy
- File upload security

## 🚀 Deployment

### Production Checklist
- [ ] Set `FLASK_ENV=production`
- [ ] Use strong `SECRET_KEY`
- [ ] Configure HTTPS
- [ ] Set up proper database
- [ ] Configure email settings
- [ ] Set up file storage
- [ ] Configure logging
- [ ] Set up monitoring

### Deployment Options
- **Heroku**: Easy deployment with Postgres
- **DigitalOcean**: VPS with full control
- **AWS**: Scalable cloud deployment
- **Docker**: Containerized deployment

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

If you encounter any issues:

1. Check the troubleshooting section in the course
2. Review the Flask documentation
3. Search existing issues on GitHub
4. Create a new issue with detailed information

## 🙏 Acknowledgments

- Flask community for excellent documentation
- Bootstrap team for the UI framework
- Contributors and testers

---

**Happy Coding! 🚀**

Start exploring the e-commerce features or dive into the Flask course to learn how it's all built!
