#!/usr/bin/env python3
"""
Simple Flask E-commerce Demo
This is a simplified version that works without MySQL setup
"""

from flask import Flask, render_template_string, request, redirect, url_for, session, flash
from datetime import datetime
import secrets

app = Flask(__name__)
app.secret_key = secrets.token_hex(16)

# Sample data (in-memory)
products = [
    {
        'id': 1,
        'name': 'Wireless Bluetooth Headphones',
        'price': 79.99,
        'image': 'headphones.jpg',
        'category': 'Electronics',
        'description': 'High-quality wireless Bluetooth headphones with noise cancellation.'
    },
    {
        'id': 2,
        'name': 'Cotton T-Shirt',
        'price': 19.99,
        'image': 'tshirt.jpg',
        'category': 'Clothing',
        'description': '100% cotton t-shirt, comfortable and breathable.'
    },
    {
        'id': 3,
        'name': 'Coffee Maker',
        'price': 89.99,
        'image': 'coffee-maker.jpg',
        'category': 'Home & Garden',
        'description': 'Programmable coffee maker with 12-cup capacity.'
    },
    {
        'id': 4,
        'name': 'Running Shoes',
        'price': 89.99,
        'image': 'shoes.jpg',
        'category': 'Sports',
        'description': 'Lightweight running shoes with excellent cushioning.'
    }
]

users = {
    '<EMAIL>': {'password': 'admin123', 'name': 'Admin User', 'is_admin': True},
    '<EMAIL>': {'password': 'password123', 'name': 'John Doe', 'is_admin': False}
}

# Templates
BASE_TEMPLATE = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}FlaskShop Demo{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .product-card { height: 100%; }
        .product-card img { height: 200px; object-fit: cover; }
        .navbar-brand { font-weight: bold; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('home') }}">
                <i class="fas fa-store me-2"></i>FlaskShop Demo
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('home') }}">Home</a>
                <a class="nav-link" href="{{ url_for('cart') }}">
                    <i class="fas fa-shopping-cart"></i> Cart ({{ session.get('cart', {})|length }})
                </a>
                {% if session.get('user') %}
                    <span class="nav-link">Hello, {{ session.user.name }}!</span>
                    <a class="nav-link" href="{{ url_for('logout') }}">Logout</a>
                {% else %}
                    <a class="nav-link" href="{{ url_for('login') }}">Login</a>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

HOME_TEMPLATE = '''
{% extends "base.html" %}
{% block content %}
<div class="jumbotron bg-primary text-white p-5 rounded mb-4">
    <h1 class="display-4">Welcome to FlaskShop Demo!</h1>
    <p class="lead">This is a simplified demo of the complete e-commerce website.</p>
    <p>Features: Product browsing, shopping cart, user authentication, and more!</p>
</div>

<h2 class="mb-4">Featured Products</h2>
<div class="row">
    {% for product in products %}
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card product-card">
            <div class="card-body">
                <h5 class="card-title">{{ product.name }}</h5>
                <p class="text-muted">{{ product.category }}</p>
                <p class="card-text">{{ product.description[:50] }}...</p>
                <p class="h5 text-primary">${{ "%.2f"|format(product.price) }}</p>
                <a href="{{ url_for('add_to_cart', product_id=product.id) }}" class="btn btn-primary">
                    <i class="fas fa-cart-plus"></i> Add to Cart
                </a>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endblock %}
'''

LOGIN_TEMPLATE = '''
{% extends "base.html" %}
{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title text-center">Login</h2>
                <form method="POST">
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <input type="email" name="email" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Password</label>
                        <input type="password" name="password" class="form-control" required>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">Login</button>
                </form>
                <div class="mt-3 text-center">
                    <small class="text-muted">
                        Demo accounts:<br>
                        Admin: <EMAIL> / admin123<br>
                        User: <EMAIL> / password123
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
'''

CART_TEMPLATE = '''
{% extends "base.html" %}
{% block content %}
<h2>Shopping Cart</h2>
{% if cart_items %}
    <div class="row">
        <div class="col-md-8">
            {% for item in cart_items %}
            <div class="card mb-3">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5>{{ item.name }}</h5>
                            <p class="text-muted">{{ item.category }}</p>
                        </div>
                        <div class="col-md-2">
                            <strong>${{ "%.2f"|format(item.price) }}</strong>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ url_for('remove_from_cart', product_id=item.id) }}" class="btn btn-sm btn-danger">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5>Order Summary</h5>
                    <p>Items: {{ cart_items|length }}</p>
                    <p>Total: <strong>${{ "%.2f"|format(total) }}</strong></p>
                    <button class="btn btn-success w-100" onclick="alert('Checkout feature coming soon!')">
                        Proceed to Checkout
                    </button>
                </div>
            </div>
        </div>
    </div>
{% else %}
    <div class="text-center">
        <i class="fas fa-shopping-cart fa-5x text-muted mb-3"></i>
        <h3>Your cart is empty</h3>
        <p>Add some products to get started!</p>
        <a href="{{ url_for('home') }}" class="btn btn-primary">Continue Shopping</a>
    </div>
{% endif %}
{% endblock %}
'''

# Routes
@app.route('/')
def home():
    return render_template_string(BASE_TEMPLATE + HOME_TEMPLATE, products=products)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        email = request.form['email']
        password = request.form['password']
        
        if email in users and users[email]['password'] == password:
            session['user'] = users[email]
            session['user']['email'] = email
            flash('Login successful!', 'success')
            return redirect(url_for('home'))
        else:
            flash('Invalid credentials!', 'error')
    
    return render_template_string(BASE_TEMPLATE + LOGIN_TEMPLATE)

@app.route('/logout')
def logout():
    session.clear()
    flash('You have been logged out.', 'info')
    return redirect(url_for('home'))

@app.route('/add-to-cart/<int:product_id>')
def add_to_cart(product_id):
    product = next((p for p in products if p['id'] == product_id), None)
    if product:
        if 'cart' not in session:
            session['cart'] = {}
        
        cart = session['cart']
        if str(product_id) in cart:
            flash('Product already in cart!', 'warning')
        else:
            cart[str(product_id)] = product_id
            session['cart'] = cart
            flash(f'{product["name"]} added to cart!', 'success')
    
    return redirect(url_for('home'))

@app.route('/remove-from-cart/<int:product_id>')
def remove_from_cart(product_id):
    if 'cart' in session:
        cart = session['cart']
        if str(product_id) in cart:
            del cart[str(product_id)]
            session['cart'] = cart
            flash('Product removed from cart!', 'info')
    
    return redirect(url_for('cart'))

@app.route('/cart')
def cart():
    cart_items = []
    total = 0
    
    if 'cart' in session:
        for product_id in session['cart'].values():
            product = next((p for p in products if p['id'] == product_id), None)
            if product:
                cart_items.append(product)
                total += product['price']
    
    return render_template_string(BASE_TEMPLATE + CART_TEMPLATE, cart_items=cart_items, total=total)

if __name__ == '__main__':
    print("🚀 Starting Flask E-commerce Demo...")
    print("📍 Open your browser and go to: http://localhost:5000")
    print("👤 Demo accounts:")
    print("   Admin: <EMAIL> / admin123")
    print("   User: <EMAIL> / password123")
    print("\nPress Ctrl+C to stop the server")
    print("-" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
