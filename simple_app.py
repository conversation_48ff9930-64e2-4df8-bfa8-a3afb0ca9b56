#!/usr/bin/env python3
"""
Simple Flask E-commerce Demo
This is a simplified version that works without MySQL setup
"""

from flask import Flask, render_template_string, request, redirect, url_for, session, flash
from datetime import datetime
import secrets

app = Flask(__name__)
app.secret_key = secrets.token_hex(16)

# Sample data (in-memory)
products = [
    {
        'id': 1,
        'name': 'Wireless Bluetooth Headphones',
        'price': 79.99,
        'image': 'headphones.jpg',
        'category': 'Electronics',
        'description': 'High-quality wireless Bluetooth headphones with noise cancellation.'
    },
    {
        'id': 2,
        'name': 'Cotton T-Shirt',
        'price': 19.99,
        'image': 'tshirt.jpg',
        'category': 'Clothing',
        'description': '100% cotton t-shirt, comfortable and breathable.'
    },
    {
        'id': 3,
        'name': 'Coffee Maker',
        'price': 89.99,
        'image': 'coffee-maker.jpg',
        'category': 'Home & Garden',
        'description': 'Programmable coffee maker with 12-cup capacity.'
    },
    {
        'id': 4,
        'name': 'Running Shoes',
        'price': 89.99,
        'image': 'shoes.jpg',
        'category': 'Sports',
        'description': 'Lightweight running shoes with excellent cushioning.'
    }
]

users = {
    '<EMAIL>': {'password': 'admin123', 'name': 'Admin User', 'is_admin': True},
    '<EMAIL>': {'password': 'password123', 'name': 'John Doe', 'is_admin': False}
}

# Course data
course_parts = [
    {
        'title': 'Part 1: Flask Fundamentals',
        'description': 'Learn the basics of Flask web development',
        'lessons': [
            {'id': 1, 'title': 'Introduction to Flask', 'duration': '30 min'},
            {'id': 2, 'title': 'Your First Flask App', 'duration': '45 min'},
            {'id': 3, 'title': 'Routing and URL Building', 'duration': '40 min'},
            {'id': 4, 'title': 'Templates with Jinja2', 'duration': '50 min'},
            {'id': 5, 'title': 'Static Files', 'duration': '25 min'},
            {'id': 6, 'title': 'HTTP Methods', 'duration': '35 min'},
        ]
    },
    {
        'title': 'Part 2: Forms and User Input',
        'description': 'Handle user input with forms and validation',
        'lessons': [
            {'id': 7, 'title': 'HTML Forms', 'duration': '40 min'},
            {'id': 8, 'title': 'WTForms Introduction', 'duration': '45 min'},
            {'id': 9, 'title': 'Form Validation', 'duration': '50 min'},
            {'id': 10, 'title': 'File Uploads', 'duration': '35 min'},
        ]
    },
    {
        'title': 'Part 3: Sessions and Cookies',
        'description': 'Manage user state and sessions',
        'lessons': [
            {'id': 11, 'title': 'Understanding HTTP Sessions', 'duration': '45 min'},
            {'id': 12, 'title': 'Flask Sessions', 'duration': '50 min'},
            {'id': 13, 'title': 'Cookies in Flask', 'duration': '40 min'},
            {'id': 14, 'title': 'Session Management', 'duration': '45 min'},
        ]
    },
    {
        'title': 'Part 4: Database Integration',
        'description': 'Work with databases using SQLAlchemy',
        'lessons': [
            {'id': 15, 'title': 'Database Basics', 'duration': '40 min'},
            {'id': 16, 'title': 'SQLAlchemy Introduction', 'duration': '55 min'},
            {'id': 17, 'title': 'Models and Relationships', 'duration': '60 min'},
            {'id': 18, 'title': 'Database Migrations', 'duration': '45 min'},
        ]
    },
    {
        'title': 'Part 5: User Authentication',
        'description': 'Implement secure user authentication',
        'lessons': [
            {'id': 19, 'title': 'User Registration', 'duration': '50 min'},
            {'id': 20, 'title': 'Login System', 'duration': '55 min'},
            {'id': 21, 'title': 'Flask-Login', 'duration': '45 min'},
            {'id': 22, 'title': 'Password Security', 'duration': '40 min'},
            {'id': 23, 'title': 'User Roles', 'duration': '50 min'},
        ]
    },
    {
        'title': 'Part 6: Advanced Flask',
        'description': 'Advanced topics and best practices',
        'lessons': [
            {'id': 24, 'title': 'Blueprints', 'duration': '55 min'},
            {'id': 25, 'title': 'Error Handling', 'duration': '40 min'},
            {'id': 26, 'title': 'Logging and Debugging', 'duration': '45 min'},
            {'id': 27, 'title': 'Testing', 'duration': '60 min'},
            {'id': 28, 'title': 'RESTful APIs', 'duration': '65 min'},
            {'id': 29, 'title': 'Security', 'duration': '50 min'},
            {'id': 30, 'title': 'Deployment', 'duration': '55 min'},
        ]
    }
]

# Sample lesson content
lessons_content = {
    1: {
        'title': 'Introduction to Flask',
        'level': 'Beginner',
        'duration': '30 min',
        'topics': ['Flask basics', 'Web frameworks', 'Python web development'],
        'objectives': [
            'Understand what Flask is and why it\'s popular',
            'Know the advantages and disadvantages of Flask',
            'Set up your development environment',
            'Understand Flask\'s philosophy and architecture'
        ],
        'content': '''
        <h3>What is Flask?</h3>
        <p>Flask is a lightweight and flexible Python web framework that provides the basic tools and libraries to build web applications. It's called a "micro" framework because it doesn't require particular tools or libraries and keeps the core simple but extensible.</p>

        <h4>Key Characteristics:</h4>
        <ul>
            <li><strong>Minimalist:</strong> Provides only the essentials</li>
            <li><strong>Flexible:</strong> Doesn't make many decisions for you</li>
            <li><strong>Extensible:</strong> Easy to add functionality through extensions</li>
            <li><strong>Pythonic:</strong> Follows Python conventions and idioms</li>
        </ul>

        <h3>Why Choose Flask?</h3>
        <h4>Advantages:</h4>
        <ul>
            <li>Simple and Easy to Learn</li>
            <li>Flexible and Lightweight</li>
            <li>Extensible with rich ecosystem</li>
            <li>Well documented</li>
            <li>Testing friendly</li>
        </ul>
        ''',
        'code_example': '''from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello_world():
    return 'Hello, World!'

if __name__ == '__main__':
    app.run(debug=True)''',
        'exercise': '''
        <p><strong>Exercise:</strong> Set up your development environment</p>
        <ol>
            <li>Install Python 3.8 or higher</li>
            <li>Create a virtual environment</li>
            <li>Install Flask</li>
            <li>Create your first "Hello World" Flask app</li>
        </ol>
        '''
    },
    2: {
        'title': 'Your First Flask App',
        'level': 'Beginner',
        'duration': '45 min',
        'topics': ['Flask app structure', 'Routes', 'View functions'],
        'objectives': [
            'Create your first Flask application',
            'Understand Flask app structure',
            'Learn about routes and view functions',
            'Run a Flask development server'
        ],
        'content': '''
        <h3>Creating Your First Flask App</h3>
        <p>Let's create the simplest possible Flask application and understand each component.</p>

        <h4>Understanding the Code:</h4>
        <ol>
            <li><strong>Import Flask:</strong> Import the Flask class from the flask module</li>
            <li><strong>Create Application Instance:</strong> Flask(__name__) creates an instance</li>
            <li><strong>Define Routes:</strong> @app.route('/') decorator maps URLs to functions</li>
            <li><strong>Run Application:</strong> app.run() starts the development server</li>
        </ol>

        <h3>The Request-Response Cycle</h3>
        <p>Understanding how Flask handles requests:</p>
        <ol>
            <li>Browser sends HTTP request to Flask server</li>
            <li>Flask receives request and matches URL to route</li>
            <li>Flask calls the corresponding view function</li>
            <li>View function processes request and returns response</li>
            <li>Flask sends HTTP response back to browser</li>
        </ol>
        ''',
        'code_example': '''from flask import Flask

app = Flask(__name__)

@app.route('/')
def home():
    return 'Welcome to the homepage!'

@app.route('/about')
def about():
    return 'This is the about page!'

@app.route('/contact')
def contact():
    return 'Contact us at: <EMAIL>'

if __name__ == '__main__':
    app.run(debug=True)''',
        'exercise': '''
        <p><strong>Exercise:</strong> Create a personal website</p>
        <ol>
            <li>Create routes for: /, /about, /projects, /contact</li>
            <li>Add HTML content to each route</li>
            <li>Include navigation links between pages</li>
            <li>Add a route that shows current time</li>
        </ol>
        '''
    },
    11: {
        'title': 'Understanding HTTP Sessions',
        'level': 'Intermediate',
        'duration': '45 min',
        'topics': ['HTTP protocol', 'Sessions', 'State management'],
        'objectives': [
            'Understand what HTTP sessions are and why they\'re needed',
            'Learn the difference between stateless and stateful protocols',
            'Understand various session storage mechanisms',
            'Know when and how to use sessions effectively'
        ],
        'content': '''
        <h3>HTTP: A Stateless Protocol</h3>
        <p>HTTP (HyperText Transfer Protocol) is <strong>stateless</strong>, meaning each request is independent and the server doesn't remember previous requests.</p>

        <h4>Example of Stateless Behavior:</h4>
        <pre>
Request 1: GET /login → Server responds with login form
Request 2: POST /login → Server authenticates user
Request 3: GET /profile → Server doesn't know user is logged in!
        </pre>

        <h3>What Are Sessions?</h3>
        <p><strong>Sessions</strong> are a way to store information about a user across multiple HTTP requests. They create the illusion of a persistent connection between the client and server.</p>

        <h4>Session Lifecycle:</h4>
        <ol>
            <li>User visits website</li>
            <li>Server creates unique session ID</li>
            <li>Session ID sent to client (usually via cookie)</li>
            <li>Client includes session ID in subsequent requests</li>
            <li>Server uses session ID to retrieve user data</li>
            <li>Session expires or is destroyed</li>
        </ol>

        <h3>Session Storage Mechanisms</h3>
        <h4>Server-Side Storage:</h4>
        <ul>
            <li><strong>File-Based:</strong> Session data stored in files on server</li>
            <li><strong>Database:</strong> Session data stored in database tables</li>
            <li><strong>Memory:</strong> Session data stored in server RAM</li>
        </ul>

        <h4>Client-Side Storage:</h4>
        <ul>
            <li><strong>Signed Cookies:</strong> Data stored in encrypted/signed cookies</li>
        </ul>
        ''',
        'code_example': '''# Pseudo-code for session-based authentication
def login(username, password):
    if authenticate(username, password):
        session['user_id'] = get_user_id(username)
        session['logged_in'] = True
        return redirect('/dashboard')
    return render_template('login.html', error='Invalid credentials')

def logout():
    session.clear()  # Remove all session data
    return redirect('/login')

def dashboard():
    if not session.get('logged_in'):
        return redirect('/login')
    user = get_user(session['user_id'])
    return render_template('dashboard.html', user=user)''',
        'exercise': '''
        <p><strong>Exercise:</strong> Session concepts</p>
        <ol>
            <li>Explain why HTTP is stateless and what problems this creates</li>
            <li>Describe the session lifecycle in your own words</li>
            <li>Compare server-side vs client-side session storage</li>
            <li>Think of 3 real-world examples where sessions are essential</li>
        </ol>
        '''
    },
    12: {
        'title': 'Flask Sessions',
        'level': 'Intermediate',
        'duration': '50 min',
        'topics': ['Flask sessions', 'Session configuration', 'Practical examples'],
        'objectives': [
            'Implement sessions in Flask applications',
            'Store and retrieve session data',
            'Configure session settings',
            'Handle session security'
        ],
        'content': '''
        <h3>Setting Up Flask Sessions</h3>
        <p>Flask provides built-in session support that's easy to use and secure.</p>

        <h4>Basic Requirements:</h4>
        <ul>
            <li><strong>Secret Key:</strong> Required for session security</li>
            <li><strong>Session Object:</strong> Import from flask</li>
            <li><strong>Configuration:</strong> Optional session settings</li>
        </ul>

        <h3>Working with Session Data</h3>
        <p>Flask sessions work like a dictionary that persists across requests.</p>

        <h4>Common Session Operations:</h4>
        <ul>
            <li><code>session['key'] = value</code> - Store data</li>
            <li><code>session.get('key', default)</code> - Retrieve data</li>
            <li><code>session.pop('key', None)</code> - Remove specific key</li>
            <li><code>session.clear()</code> - Clear all session data</li>
            <li><code>session.permanent = True</code> - Make session permanent</li>
        </ul>

        <h3>Session Security</h3>
        <p>Important security considerations:</p>
        <ul>
            <li>Always use a strong secret key</li>
            <li>Use HTTPS in production</li>
            <li>Set appropriate cookie flags</li>
            <li>Implement session timeout</li>
            <li>Validate session data</li>
        </ul>
        ''',
        'code_example': '''from flask import Flask, session, request, redirect, url_for

app = Flask(__name__)
app.secret_key = 'your-very-secret-key-here'

@app.route('/')
def home():
    if 'username' in session:
        return f'Hello, {session["username"]}!'
    return 'You are not logged in'

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        # In real app, verify credentials here
        session['username'] = username
        session['logged_in'] = True
        return redirect(url_for('home'))

    return """
    <form method="POST">
        <input type="text" name="username" placeholder="Username" required>
        <input type="submit" value="Login">
    </form>
    """

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('home'))

if __name__ == '__main__':
    app.run(debug=True)''',
        'exercise': '''
        <p><strong>Exercise:</strong> Build a session-based feature</p>
        <ol>
            <li>Create a visit counter using sessions</li>
            <li>Build a simple shopping cart with sessions</li>
            <li>Implement user preferences (theme, language)</li>
            <li>Create a multi-step form using sessions</li>
        </ol>
        '''
    }
}

# Templates
BASE_TEMPLATE = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}FlaskShop Demo{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .product-card { height: 100%; }
        .product-card img { height: 200px; object-fit: cover; }
        .navbar-brand { font-weight: bold; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('home') }}">
                <i class="fas fa-store me-2"></i>FlaskShop Demo
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('home') }}">Home</a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-graduation-cap"></i> Flask Course
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ url_for('course_home') }}">Course Overview</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('course_lessons') }}">All Lessons</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('course_lesson', lesson_id=1) }}">Lesson 1: Introduction</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('course_lesson', lesson_id=2) }}">Lesson 2: First App</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('course_lesson', lesson_id=11) }}">Lesson 11: Sessions</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('course_lesson', lesson_id=12) }}">Lesson 12: Flask Sessions</a></li>
                    </ul>
                </div>
                <a class="nav-link" href="{{ url_for('cart') }}">
                    <i class="fas fa-shopping-cart"></i> Cart ({{ session.get('cart', {})|length }})
                </a>
                {% if session.get('user') %}
                    <span class="nav-link">Hello, {{ session.user.name }}!</span>
                    <a class="nav-link" href="{{ url_for('logout') }}">Logout</a>
                {% else %}
                    <a class="nav-link" href="{{ url_for('login') }}">Login</a>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

HOME_TEMPLATE = '''
{% extends "base.html" %}
{% block content %}
<div class="jumbotron bg-primary text-white p-5 rounded mb-4">
    <h1 class="display-4">Welcome to FlaskShop Demo!</h1>
    <p class="lead">This is a simplified demo of the complete e-commerce website.</p>
    <p>Features: Product browsing, shopping cart, user authentication, and more!</p>
</div>

<h2 class="mb-4">Featured Products</h2>
<div class="row">
    {% for product in products %}
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card product-card">
            <div class="card-body">
                <h5 class="card-title">{{ product.name }}</h5>
                <p class="text-muted">{{ product.category }}</p>
                <p class="card-text">{{ product.description[:50] }}...</p>
                <p class="h5 text-primary">${{ "%.2f"|format(product.price) }}</p>
                <a href="{{ url_for('add_to_cart', product_id=product.id) }}" class="btn btn-primary">
                    <i class="fas fa-cart-plus"></i> Add to Cart
                </a>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endblock %}
'''

LOGIN_TEMPLATE = '''
{% extends "base.html" %}
{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title text-center">Login</h2>
                <form method="POST">
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <input type="email" name="email" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Password</label>
                        <input type="password" name="password" class="form-control" required>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">Login</button>
                </form>
                <div class="mt-3 text-center">
                    <small class="text-muted">
                        Demo accounts:<br>
                        Admin: <EMAIL> / admin123<br>
                        User: <EMAIL> / password123
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
'''

CART_TEMPLATE = '''
{% extends "base.html" %}
{% block content %}
<h2>Shopping Cart</h2>
{% if cart_items %}
    <div class="row">
        <div class="col-md-8">
            {% for item in cart_items %}
            <div class="card mb-3">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5>{{ item.name }}</h5>
                            <p class="text-muted">{{ item.category }}</p>
                        </div>
                        <div class="col-md-2">
                            <strong>${{ "%.2f"|format(item.price) }}</strong>
                        </div>
                        <div class="col-md-2">
                            <a href="{{ url_for('remove_from_cart', product_id=item.id) }}" class="btn btn-sm btn-danger">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5>Order Summary</h5>
                    <p>Items: {{ cart_items|length }}</p>
                    <p>Total: <strong>${{ "%.2f"|format(total) }}</strong></p>
                    <button class="btn btn-success w-100" onclick="alert('Checkout feature coming soon!')">
                        Proceed to Checkout
                    </button>
                </div>
            </div>
        </div>
    </div>
{% else %}
    <div class="text-center">
        <i class="fas fa-shopping-cart fa-5x text-muted mb-3"></i>
        <h3>Your cart is empty</h3>
        <p>Add some products to get started!</p>
        <a href="{{ url_for('home') }}" class="btn btn-primary">Continue Shopping</a>
    </div>
{% endif %}
{% endblock %}
'''

COURSE_HOME_TEMPLATE = '''
{% extends "base.html" %}
{% block content %}
<div class="row">
    <div class="col-md-8">
        <h1><i class="fas fa-graduation-cap text-primary"></i> Complete Flask Course</h1>
        <p class="lead">Learn Flask from beginner to master level with hands-on examples and real-world projects.</p>

        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle"></i> Course Overview</h5>
            <p>This comprehensive course covers all aspects of Flask web development, including:</p>
            <ul>
                <li><strong>Flask Fundamentals</strong> - Routing, templates, static files</li>
                <li><strong>Forms & Validation</strong> - WTForms, user input handling</li>
                <li><strong>Sessions & Cookies</strong> - State management, user tracking</li>
                <li><strong>Database Integration</strong> - SQLAlchemy, models, relationships</li>
                <li><strong>Authentication</strong> - Login systems, user management</li>
                <li><strong>Advanced Topics</strong> - APIs, security, deployment</li>
            </ul>
        </div>

        <h3>Course Structure</h3>
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">Part 1: Fundamentals</h5>
                        <p class="card-text">Lessons 1-6: Basic Flask concepts, routing, templates</p>
                        <a href="{{ url_for('course_lesson', lesson_id=1) }}" class="btn btn-primary btn-sm">Start Here</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">Part 2: Forms & Input</h5>
                        <p class="card-text">Lessons 7-10: HTML forms, WTForms, validation</p>
                        <a href="{{ url_for('course_lesson', lesson_id=7) }}" class="btn btn-outline-primary btn-sm">View</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">Part 3: Sessions & Cookies</h5>
                        <p class="card-text">Lessons 11-14: State management, user sessions</p>
                        <a href="{{ url_for('course_lesson', lesson_id=11) }}" class="btn btn-outline-primary btn-sm">View</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">Part 4: Database</h5>
                        <p class="card-text">Lessons 15-18: SQLAlchemy, models, migrations</p>
                        <a href="{{ url_for('course_lesson', lesson_id=15) }}" class="btn btn-outline-primary btn-sm">View</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5>Quick Navigation</h5>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('course_lessons') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-list"></i> All Lessons
                    </a>
                    <a href="{{ url_for('course_lesson', lesson_id=1) }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-play"></i> Start Course
                    </a>
                    <a href="{{ url_for('course_lesson', lesson_id=11) }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-cookie-bite"></i> Sessions & Cookies
                    </a>
                    <a href="{{ url_for('course_lesson', lesson_id=20) }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-lock"></i> Login Systems
                    </a>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-body">
                <h6>Course Progress</h6>
                <div class="progress mb-2">
                    <div class="progress-bar" style="width: 0%"></div>
                </div>
                <small class="text-muted">0 of 30 lessons completed</small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
'''

COURSE_LESSONS_TEMPLATE = '''
{% extends "base.html" %}
{% block content %}
<h1><i class="fas fa-list"></i> All Flask Course Lessons</h1>
<p class="lead">Complete curriculum from beginner to advanced Flask development.</p>

<div class="row">
    {% for part in course_parts %}
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">{{ part.title }}</h5>
            </div>
            <div class="card-body">
                <p class="card-text">{{ part.description }}</p>
                <div class="list-group list-group-flush">
                    {% for lesson in part.lessons %}
                    <a href="{{ url_for('course_lesson', lesson_id=lesson.id) }}"
                       class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span>
                            <i class="fas fa-book-open me-2"></i>
                            Lesson {{ lesson.id }}: {{ lesson.title }}
                        </span>
                        <span class="badge bg-secondary">{{ lesson.duration }}</span>
                    </a>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endblock %}
'''

COURSE_LESSON_TEMPLATE = '''
{% extends "base.html" %}
{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('course_home') }}">Course</a></li>
        <li class="breadcrumb-item active">Lesson {{ lesson.id }}</li>
    </ol>
</nav>

<div class="row">
    <div class="col-md-9">
        <h1>{{ lesson.title }}</h1>
        <div class="alert alert-primary">
            <h5><i class="fas fa-target"></i> Learning Objectives</h5>
            <ul>
                {% for objective in lesson.objectives %}
                <li>{{ objective }}</li>
                {% endfor %}
            </ul>
        </div>

        <div class="lesson-content">
            {{ lesson.content | safe }}
        </div>

        {% if lesson.code_example %}
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-code"></i> Code Example</h5>
            </div>
            <div class="card-body">
                <pre><code>{{ lesson.code_example }}</code></pre>
            </div>
        </div>
        {% endif %}

        {% if lesson.exercise %}
        <div class="card mt-4 border-warning">
            <div class="card-header bg-warning">
                <h5><i class="fas fa-dumbbell"></i> Exercise</h5>
            </div>
            <div class="card-body">
                {{ lesson.exercise | safe }}
            </div>
        </div>
        {% endif %}
    </div>

    <div class="col-md-3">
        <div class="card">
            <div class="card-body">
                <h6>Navigation</h6>
                {% if lesson.id > 1 %}
                <a href="{{ url_for('course_lesson', lesson_id=lesson.id-1) }}" class="btn btn-outline-primary btn-sm w-100 mb-2">
                    <i class="fas fa-arrow-left"></i> Previous Lesson
                </a>
                {% endif %}

                {% if lesson.id < 30 %}
                <a href="{{ url_for('course_lesson', lesson_id=lesson.id+1) }}" class="btn btn-primary btn-sm w-100 mb-2">
                    Next Lesson <i class="fas fa-arrow-right"></i>
                </a>
                {% endif %}

                <a href="{{ url_for('course_lessons') }}" class="btn btn-outline-secondary btn-sm w-100">
                    <i class="fas fa-list"></i> All Lessons
                </a>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-body">
                <h6>Lesson Info</h6>
                <small class="text-muted">
                    <i class="fas fa-clock"></i> Duration: {{ lesson.duration }}<br>
                    <i class="fas fa-signal"></i> Level: {{ lesson.level }}<br>
                    <i class="fas fa-tags"></i> Topics: {{ lesson.topics | join(', ') }}
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
'''

# Routes
@app.route('/')
def home():
    return render_template_string(BASE_TEMPLATE + HOME_TEMPLATE, products=products)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        email = request.form['email']
        password = request.form['password']
        
        if email in users and users[email]['password'] == password:
            session['user'] = users[email]
            session['user']['email'] = email
            flash('Login successful!', 'success')
            return redirect(url_for('home'))
        else:
            flash('Invalid credentials!', 'error')
    
    return render_template_string(BASE_TEMPLATE + LOGIN_TEMPLATE)

@app.route('/logout')
def logout():
    session.clear()
    flash('You have been logged out.', 'info')
    return redirect(url_for('home'))

@app.route('/add-to-cart/<int:product_id>')
def add_to_cart(product_id):
    product = next((p for p in products if p['id'] == product_id), None)
    if product:
        if 'cart' not in session:
            session['cart'] = {}
        
        cart = session['cart']
        if str(product_id) in cart:
            flash('Product already in cart!', 'warning')
        else:
            cart[str(product_id)] = product_id
            session['cart'] = cart
            flash(f'{product["name"]} added to cart!', 'success')
    
    return redirect(url_for('home'))

@app.route('/remove-from-cart/<int:product_id>')
def remove_from_cart(product_id):
    if 'cart' in session:
        cart = session['cart']
        if str(product_id) in cart:
            del cart[str(product_id)]
            session['cart'] = cart
            flash('Product removed from cart!', 'info')
    
    return redirect(url_for('cart'))

@app.route('/cart')
def cart():
    cart_items = []
    total = 0

    if 'cart' in session:
        for product_id in session['cart'].values():
            product = next((p for p in products if p['id'] == product_id), None)
            if product:
                cart_items.append(product)
                total += product['price']

    return render_template_string(BASE_TEMPLATE + CART_TEMPLATE, cart_items=cart_items, total=total)

# Course Routes
@app.route('/course')
def course_home():
    return render_template_string(BASE_TEMPLATE + COURSE_HOME_TEMPLATE)

@app.route('/course/lessons')
def course_lessons():
    return render_template_string(BASE_TEMPLATE + COURSE_LESSONS_TEMPLATE, course_parts=course_parts)

@app.route('/course/lesson/<int:lesson_id>')
def course_lesson(lesson_id):
    # Get lesson content or create default
    lesson = lessons_content.get(lesson_id, {
        'title': f'Lesson {lesson_id}',
        'level': 'Intermediate',
        'duration': '45 min',
        'topics': ['Flask development'],
        'objectives': [
            f'Learn about lesson {lesson_id} concepts',
            'Practice with hands-on examples',
            'Complete exercises to reinforce learning'
        ],
        'content': f'''
        <h3>Lesson {lesson_id} Content</h3>
        <p>This lesson is part of the comprehensive Flask course. The detailed content for this lesson is available in the course files.</p>

        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle"></i> Course Files Location</h5>
            <p>The complete lesson content can be found in:</p>
            <code>flask-course/lessons/{lesson_id:02d}-*/README.md</code>
        </div>

        <h4>What You'll Learn:</h4>
        <ul>
            <li>Core concepts for this lesson</li>
            <li>Practical implementation examples</li>
            <li>Best practices and common patterns</li>
            <li>Hands-on exercises</li>
        </ul>
        ''',
        'code_example': f'''# Example code for lesson {lesson_id}
from flask import Flask

app = Flask(__name__)

@app.route('/')
def lesson_{lesson_id}_example():
    return 'Lesson {lesson_id} example'

if __name__ == '__main__':
    app.run(debug=True)''',
        'exercise': f'''
        <p><strong>Exercise for Lesson {lesson_id}:</strong></p>
        <ol>
            <li>Review the lesson content in the course files</li>
            <li>Try the code examples provided</li>
            <li>Complete the practical exercises</li>
            <li>Experiment with variations of the examples</li>
        </ol>
        <div class="alert alert-warning">
            <strong>Note:</strong> Complete lesson content is available in the <code>flask-course/</code> directory.
        </div>
        '''
    })

    lesson['id'] = lesson_id
    return render_template_string(BASE_TEMPLATE + COURSE_LESSON_TEMPLATE, lesson=lesson)

if __name__ == '__main__':
    print("🚀 Starting Flask E-commerce Demo...")
    print("📍 Open your browser and go to: http://localhost:5000")
    print("👤 Demo accounts:")
    print("   Admin: <EMAIL> / admin123")
    print("   User: <EMAIL> / password123")
    print("\nPress Ctrl+C to stop the server")
    print("-" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
